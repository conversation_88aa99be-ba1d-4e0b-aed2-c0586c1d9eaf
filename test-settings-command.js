/**
 * Test script to verify the aizen.showSettings command is properly registered
 */

const vscode = require('vscode');

async function testSettingsCommand() {
    console.log('🧪 Testing aizen.showSettings command registration...');
    
    try {
        // Get all available commands
        const commands = await vscode.commands.getCommands();
        
        // Check if our command is registered
        const hasSettingsCommand = commands.includes('aizen.showSettings');
        
        if (hasSettingsCommand) {
            console.log('✅ aizen.showSettings command is registered!');
            
            // Try to execute the command
            try {
                await vscode.commands.executeCommand('aizen.showSettings');
                console.log('✅ aizen.showSettings command executed successfully!');
            } catch (error) {
                console.error('❌ Failed to execute aizen.showSettings:', error);
            }
        } else {
            console.error('❌ aizen.showSettings command is NOT registered!');
            console.log('Available Aizen commands:', commands.filter(cmd => cmd.startsWith('aizen.')));
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Export for use in VS Code extension context
module.exports = { testSettingsCommand };

// If running directly, show instructions
if (require.main === module) {
    console.log('📋 To test the settings command:');
    console.log('1. Open VS Code');
    console.log('2. Open Command Palette (Ctrl+Shift+P)');
    console.log('3. Run "Developer: Reload Window" to reload the extension');
    console.log('4. Try "Aizen AI: Open Settings" command');
    console.log('5. Or use Command Palette and search for "aizen.showSettings"');
}
