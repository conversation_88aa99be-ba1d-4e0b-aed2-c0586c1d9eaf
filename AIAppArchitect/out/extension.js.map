{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBH,4BA0NC;AA6CD,gCAgBC;AAzSD,+CAAiC;AACjC,4EAAyE;AACzE,gFAA6E;AAC7E,6EAA0E;AAC1E,mCAAmC;AACnC,6CAAoD;AACpD,2CAAuD;AACvD,uDAAmE;AACnE,qCAAyC;AACzC,2EAAwE;AAExE,6BAA6B;AAC7B,IAAI,gBAAuC,CAAC;AAC5C,IAAI,MAAc,CAAC;AACnB,IAAI,WAA+B,CAAC;AACpC,IAAI,uBAAgD,CAAC;AAG9C,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC3D,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;IAE7E,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC;QAEjF,kCAAkC;QAClC,WAAW,GAAG,IAAI,6BAAkB,CAAC,OAAO,CAAC,CAAC;QAE9C,iCAAiC;QACjC,MAAM,kBAAkB,GAAG,IAAI,iDAAuB,EAAE,CAAC;QAEzD,8BAA8B;QAC9B,MAAM,gBAAgB,GAAG,IAAI,6CAAqB,CAAC,OAAO,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC;QAC7F,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAChF,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAE/C,2CAA2C;QAC3C,MAAM,GAAG,IAAI,YAAM,CAAC,OAAO,CAAC,CAAC;QAC7B,MAAM,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAElD,kCAAkC;YAClC,MAAM,wBAAwB,EAAE,CAAC;QACrC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,+CAA+C;QAC/C,uBAAuB,GAAG,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,kCAAkC;QAClC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;YAC7D,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC/C,IAAI,CAAC;gBACD,2CAA2C;gBAC3C,MAAM,EAAE,qBAAqB,EAAE,GAAG,wDAAa,mCAAmC,GAAC,CAAC;gBACpF,qBAAqB,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;gBACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,CACL,CAAC;QAEF,wBAAwB;QACxB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,GAAG,EAAE;YACrD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oDAAoD,CAAC,CAAC;QAC/F,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;YAC3D,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpC,MAAM,gBAAgB,GAAG,MAAM,CAAC,mBAAmB,EAAE,CAAC;gBACtD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBAEnC,MAAM,OAAO,GAAG,kBAAkB;oBAC9B,oBAAoB,OAAO,CAAC,MAAM,IAAI;oBACtC,gBAAgB,gBAAgB,CAAC,MAAM,IAAI;oBAC3C,sBAAsB,KAAK,CAAC,MAAM,MAAM;oBACxC,uBAAuB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAE1H,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACjE,IAAI,CAAC;gBACD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC5C,MAAM,EAAE,wBAAwB;oBAChC,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,SAAS;iBACzB,CAAC,CAAC;gBAEH,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,MAAM,GAAG;wBACX,GAAG,wBAAkB;wBACrB,GAAG,EAAE;4BACD,GAAG,wBAAkB,CAAC,GAAG;4BACzB,eAAe,EAAE,UAAU,MAAM,EAAE;yBACtC;qBACJ,CAAC;oBAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+CAA+C,QAAQ,EAAE,CAAC,CAAC;gBACpG,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YACzE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8BAA8B,EAAE,KAAK,IAAI,EAAE;YACvE,IAAI,CAAC;gBACD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC5C,MAAM,EAAE,8BAA8B;oBACtC,QAAQ,EAAE,IAAI;oBACd,WAAW,EAAE,QAAQ;iBACxB,CAAC,CAAC;gBAEH,IAAI,MAAM,EAAE,CAAC;oBACT,MAAM,MAAM,GAAG;wBACX,GAAG,oCAAwB;wBAC3B,GAAG,EAAE;4BACD,GAAG,oCAAwB,CAAC,GAAG;4BAC/B,eAAe,EAAE,UAAU,MAAM,EAAE;yBACtC;qBACJ,CAAC;oBAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qDAAqD,QAAQ,EAAE,CAAC,CAAC;gBAC1G,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YAC/E,CAAC;QACL,CAAC,CAAC,EACF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;YAClE,IAAI,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACxC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;gBAE/E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,2CAA2C,CAAC,CAAC;oBAC9E,OAAO;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,gBAAgB,EAAE;oBACrE,KAAK,EAAE,6BAA6B;oBACpC,UAAU,EAAE,CAAC;iBAChB,CAAC,CAAC;gBAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;YACxH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;gBAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,KAAK,EAAE,CAAC,CAAC;YAClE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YACxE,IAAI,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAC9C,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAErF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,iDAAiD,CAAC,CAAC;oBACpF,OAAO;gBACX,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,kBAAkB,EAAE;oBACvE,GAAG,EAAE,qBAAqB;oBAC1B,OAAO,EAAE,CAAC,UAAU,CAAC;iBACxB,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;gBACpC,MAAM,aAAa,GAAG,OAAO,EAAE,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uDAAuD,aAAa,SAAS,CAAC,CAAC;YACxH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACjD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,KAAK,EAAE,CAAC,CAAC;YACxE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;YAC9D,IAAI,CAAC;gBACD,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChF,MAAM,OAAO,GAAG,0BAA0B,KAAK,CAAC,MAAM,SAAS,SAAS,IAAI,oBAAoB,EAAE,CAAC;gBACnG,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;YACrE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,KAAK,IAAI,EAAE;YAC7D,IAAI,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;gBACrD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oDAAoD,CAAC,CAAC;gBAC3F,MAAM,IAAA,kBAAW,EAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;gBAC5C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;YACjE,CAAC;QACL,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC,CAAC,EAEF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;YACjE,IAAI,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,MAAM,uBAAuB,CAAC,gBAAgB,EAAE,CAAC;YACrD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;gBACnE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4CAA4C,KAAK,EAAE,CAAC,CAAC;YACxF,CAAC;QACL,CAAC,CAAC,CACL,CAAC;QAEF,+BAA+B;QAC/B,gBAAgB,GAAG,6CAAqB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC9D,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnC,MAAM,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE/D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAC7H,CAAC;AACL,CAAC;AAED,kCAAkC;AAClC,KAAK,UAAU,wBAAwB;IACnC,IAAI,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,qCAAqC;QACrC,MAAM,SAAS,GAAG;YACd,GAAG,wBAAkB;YACrB,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,GAAG,EAAE;gBACD,cAAc,EAAE,kBAAkB;gBAClC,eAAe,EAAE,6CAA6C;gBAC9D,aAAa,EAAE,sCAAsC;aACxD;SACJ,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC;QAEtD,2CAA2C;QAC3C,MAAM,eAAe,GAAG;YACpB,GAAG,oCAAwB;YAC3B,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI;YACf,GAAG,EAAE;gBACD,cAAc,EAAE,kBAAkB;gBAClC,eAAe,EAAE,4CAA4C;gBAC7D,mBAAmB,EAAE,qCAAqC;aAC7D;SACJ,CAAC;QAEF,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,iBAAiB,CAAC,CAAC;QAElE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sDAAsD,CAAC,CAAC;IAEjG,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;IACjF,CAAC;AACL,CAAC;AAEM,KAAK,UAAU,UAAU;IAC5B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,IAAI,gBAAgB,EAAE,CAAC;QACnB,MAAM,gBAAgB,CAAC,UAAU,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;AACpD,CAAC"}