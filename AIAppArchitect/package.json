{"name": "aizen-revolutionary-ai", "displayName": "Aizen Revolutionary AI", "description": "The world's most advanced AI-powered code editor with RSI, HyperGraph RAG, and Swarm Intelligence", "version": "2.0.0", "publisher": "aizen-ai", "icon": "./media/icons/extension-icon-128.png", "engines": {"vscode": "^1.100.0"}, "categories": ["Machine Learning", "Programming Languages", "Debuggers", "Other"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "aizen-ai", "title": "Aizen AI", "icon": "./media/icons/aizen-logo-white.svg"}]}, "views": {"aizen-ai": [{"id": "aizen.chatView", "name": "AI Chat", "type": "webview", "when": "true"}]}, "commands": [{"command": "aizen.showChatView", "title": "Open AI Chat", "category": "Aizen AI", "icon": "./media/icons/aizen-logo.svg"}, {"command": "aizen.showSettings", "title": "Open Settings", "category": "Aizen AI"}, {"command": "aizen.openInlineChat", "title": "Start Inline Chat", "category": "Aizen AI", "icon": "./media/icons/aizen-logo.svg"}, {"command": "aizen.quickChat", "title": "Quick Chat", "category": "Aizen AI", "icon": "./media/icons/aizen-logo.svg"}], "keybindings": [{"command": "aizen.showChatView", "key": "ctrl+k", "mac": "cmd+k", "when": "!inQuickOpen && !terminalFocus"}, {"command": "aizen.openInlineChat", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus"}, {"command": "aizen.quickChat", "key": "ctrl+shift+l", "mac": "cmd+shift+l"}], "menus": {"editor/context": [{"command": "aizen.openInlineChat", "when": "editorTextFocus", "group": "1_modification@1"}], "editor/title": [{"command": "aizen.showChatView", "when": "true", "group": "navigation@1"}], "commandPalette": [{"command": "aizen.showChatView", "when": "true"}, {"command": "aizen.openInlineChat", "when": "editorTextFocus"}, {"command": "aizen.quickChat", "when": "true"}]}, "configuration": {"title": "Aizen Revolutionary AI", "properties": {"aizen.rsi.enabled": {"type": "boolean", "default": false, "description": "Enable Recursive Self-Improvement engine"}, "aizen.swarm.maxAgents": {"type": "number", "default": 50, "description": "Maximum number of agents in a swarm"}, "aizen.performance.targetLatency": {"type": "number", "default": 50, "description": "Target response latency in milliseconds"}}}}, "scripts": {"vscode:prepublish": "npm run build", "build": "npm run compile && npm run copy-ui", "compile": "tsc -p ./", "copy-ui": "mkdir -p ../out/ui && cp src/ui/*.css ../out/ui/ && cp src/ui/*.html ../out/ui/ && cp src/ui/*.js ../out/ui/", "watch": "tsc -watch -p ./", "webpack": "npx webpack --mode development", "webpack-dev": "npx webpack --mode development --watch", "webpack:prod": "npx webpack --mode production", "build-rust": "cargo build --release", "test": "node test_competitive_features.js", "package": "vsce package", "install-extension": "code --install-extension aizen-revolutionary-ai-2.0.0.vsix"}, "keywords": ["ai", "machine-learning", "agents", "swarm-intelligence", "rsi", "hypergraph", "voice-control", "time-travel-debugging"], "author": "Aizen AI Team", "license": "MIT", "dependencies": {"uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.18.0", "@babel/plugin-proposal-object-rest-spread": "^7.20.0", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@types/node": "^22.15.30", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^10.0.0", "@types/vscode": "^1.100.0", "@types/ws": "^8.5.0", "@vscode/vsce": "^2.24.0", "babel-loader": "^9.2.1", "cargo-cp-artifact": "^0.1.8", "css-loader": "^6.11.0", "html-webpack-plugin": "^5.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "redis": "^4.6.0", "simple-git": "^3.28.0", "style-loader": "^3.3.4", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "vscode": "^1.1.34", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}, "repository": {"type": "git", "url": "https://github.com/aizen-ai/revolutionary-ai-extension.git"}, "bugs": {"url": "https://github.com/aizen-ai/revolutionary-ai-extension/issues"}, "homepage": "https://github.com/aizen-ai/revolutionary-ai-extension#readme"}