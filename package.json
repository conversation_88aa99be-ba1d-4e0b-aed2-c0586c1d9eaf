{"name": "aizen-revolutionary-ai", "displayName": "Aizen Revolutionary AI", "description": "The world's most advanced AI-powered code editor with RSI, HyperGraph RAG, and Swarm Intelligence", "version": "2.0.0", "publisher": "aizen-ai", "engines": {"vscode": "^1.101.0"}, "categories": ["Machine Learning", "Programming Languages", "Debuggers", "Other"], "activationEvents": ["onStartupFinished", "onCommand:aizen.showSettings"], "main": "./out/extension.js", "contributes": {"configuration": {"title": "Aizen AI", "properties": {"aizen.model.provider": {"type": "string", "enum": ["Anthropic", "OpenAI", "Google", "Local"], "default": "Anthropic", "description": "AI model provider to use", "enumDescriptions": ["Use Anthropic Claude models", "Use OpenAI GPT models", "Use Google Gemini models", "Use local models"]}, "aizen.model.name": {"type": "string", "default": "claude-3-5-sonnet-20241022", "description": "Specific model name to use"}, "aizen.model.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 2, "description": "Controls randomness in AI responses (0 = deterministic, 2 = very creative)"}, "aizen.model.maxTokens": {"type": "number", "default": 4096, "minimum": 1, "maximum": 200000, "description": "Maximum number of tokens in AI responses"}, "aizen.api.anthropicKey": {"type": "string", "default": "", "description": "Anthropic API key for Claude models"}, "aizen.api.openaiKey": {"type": "string", "default": "", "description": "OpenAI API key for GPT models"}, "aizen.api.googleKey": {"type": "string", "default": "", "description": "Google API key for Gemini models"}, "aizen.mcp.exaEnabled": {"type": "boolean", "default": true, "description": "Enable Exa MCP server for web search and research"}, "aizen.mcp.firecrawlEnabled": {"type": "boolean", "default": true, "description": "Enable Firecrawl MCP server for web scraping"}, "aizen.mcp.exaApiKey": {"type": "string", "default": "", "description": "Exa API key for search functionality"}, "aizen.mcp.firecrawlApiKey": {"type": "string", "default": "", "description": "Firecrawl API key for web scraping"}, "aizen.ui.theme": {"type": "string", "enum": ["auto", "light", "dark"], "default": "auto", "description": "UI theme preference", "enumDescriptions": ["Follow VS Code theme", "Always use light theme", "Always use dark theme"]}, "aizen.ui.fontSize": {"type": "string", "enum": ["small", "medium", "large"], "default": "medium", "description": "UI font size preference"}, "aizen.features.autoSave": {"type": "boolean", "default": true, "description": "Automatically save settings when changed"}, "aizen.features.notifications": {"type": "boolean", "default": true, "description": "Show notifications for important events"}}}, "viewsContainers": {"activitybar": [{"id": "aizen-ai", "title": "Aizen AI", "icon": "./media/icons/aizen-logo-white.svg"}]}, "views": {"aizen-ai": [{"id": "aizen.chatView", "name": "AI Chat", "type": "webview", "icon": "$(robot)", "when": "true"}]}, "commands": [{"command": "aizen.showChatView", "title": "Open AI Chat", "category": "Aizen AI", "icon": "./media/icons/aizen-logo.svg"}, {"command": "aizen.showSettings", "title": "Open Settings", "category": "Aizen AI"}, {"command": "aizen.test", "title": "Test Extension", "category": "Aizen AI"}, {"command": "aizen.test.simple", "title": "Simple Test", "category": "Aizen AI"}, {"command": "aizen.test.simple", "title": "Simple Test", "category": "Aizen AI"}, {"command": "aizen.test.simple", "title": "Simple Test", "category": "Aizen AI"}, {"command": "aizen.test.settings", "title": "Test Settings Command", "category": "Aizen AI"}, {"command": "aizen.force.settings", "title": "Force Open Settings", "category": "Aizen AI"}, {"command": "aizen.mcp.openSettings", "title": "Open MCP Settings", "category": "Aizen MCP", "icon": "$(settings-gear)"}, {"command": "aizen.mcp.configEditor", "title": "Configure MCP Servers", "category": "Aizen MCP", "icon": "$(edit)"}, {"command": "aizen.mcp.status", "title": "Show MCP Server Status", "category": "Aizen MCP"}, {"command": "aizen.mcp.addExternalServer", "title": "Add External MCP Server", "category": "Aizen MCP"}, {"command": "aizen.mcp.testExaSearch", "title": "Test Exa Search", "category": "Aizen MCP"}, {"command": "aizen.mcp.testFirecrawlScrape", "title": "Test Firecrawl Scrape", "category": "Aizen MCP"}, {"command": "aizen.mcp.listTools", "title": "List Available MCP Tools", "category": "Aizen MCP"}, {"command": "aizen.mcp.runTests", "title": "Run MCP Integration Tests", "category": "Aizen MCP"}], "keybindings": [{"command": "aizen.showChatView", "key": "ctrl+k", "mac": "cmd+k", "when": "!inQuickOpen && !terminalFocus"}]}, "scripts": {"vscode:prepublish": "cd AIAppArchitect && npm run build", "build": "cd AIAppArchitect && npm run build", "package": "cd AIAppArchitect && npm run package"}, "devDependencies": {"@types/node": "^24.0.4", "@types/vscode": "^1.101.0", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "axios": "^1.10.0", "exa-mcp-server": "^0.3.10", "firecrawl-mcp": "^1.11.0", "mcp-remote": "^0.1.17", "ws": "^8.18.3", "zod": "^3.25.67"}}