// Exa MCP Server Integration
import { MCPServerConfig } from '../types';

export const ExaMCPServerConfig: MCPServerConfig = {
    name: 'Exa AI Search',
    url: 'https://api.exa.ai/mcp/v1',
    transport: 'streamable-http',
    enabled: true,
    autoStart: true,
    timeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    env: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer f01e507f-cdd2-454d-adcf-545d24035692',
        'EXA_API_KEY': 'f01e507f-cdd2-454d-adcf-545d24035692'
    }
};

export interface ExaSearchParams {
    query: string;
    numResults?: number;
    includeDomains?: string[];
    excludeDomains?: string[];
    startCrawlDate?: string;
    endCrawlDate?: string;
    startPublishedDate?: string;
    endPublishedDate?: string;
    useAutoprompt?: boolean;
    type?: 'neural' | 'keyword';
    category?: string;
    includeText?: boolean;
    includeHighlights?: boolean;
    includeSummary?: boolean;
    livecrawl?: boolean;
    priority?: 'speed' | 'quality';
}

export interface ExaSearchResult {
    id: string;
    url: string;
    title: string;
    score: number;
    publishedDate?: string;
    author?: string;
    text?: string;
    highlights?: string[];
    summary?: string;
}

export interface ExaContentsParams {
    ids: string[];
    text?: boolean;
    highlights?: boolean;
    summary?: boolean;
}

export interface ExaFindSimilarParams {
    url: string;
    numResults?: number;
    includeDomains?: string[];
    excludeDomains?: string[];
    startCrawlDate?: string;
    endCrawlDate?: string;
    startPublishedDate?: string;
    endPublishedDate?: string;
    excludeSourceDomain?: boolean;
    category?: string;
    includeText?: boolean;
    includeHighlights?: boolean;
    includeSummary?: boolean;
}

// Exa MCP Tool Definitions
export const ExaMCPTools = [
    {
        name: 'web_search_exa',
        description: 'Search the web using Exa AI - performs real-time web searches and can scrape content from specific URLs. Supports configurable result counts and returns the content from the most relevant websites.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of search results to return (default: 5)',
                    default: 5
                },
                includeDomains: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Domains to include in search'
                },
                excludeDomains: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Domains to exclude from search'
                },
                includeText: {
                    type: 'boolean',
                    description: 'Include full text content',
                    default: true
                },
                useAutoprompt: {
                    type: 'boolean',
                    description: 'Use Exa autoprompt for better results',
                    default: true
                },
                type: {
                    type: 'string',
                    enum: ['neural', 'keyword'],
                    description: 'Search type',
                    default: 'neural'
                }
            },
            required: ['query']
        }
    },
    {
        name: 'research_paper_search_exa',
        description: 'Search across 100M+ research papers with full text access using Exa AI - performs targeted academic paper searches with deep research content coverage.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Research topic or keyword to search for'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of research papers to return (default: 5)',
                    default: 5
                },
                maxCharacters: {
                    type: 'number',
                    description: 'Maximum number of characters to return for each result\'s text content (Default: 3000)',
                    default: 3000
                },
                startPublishedDate: {
                    type: 'string',
                    description: 'Start date for published papers (YYYY-MM-DD)'
                },
                endPublishedDate: {
                    type: 'string',
                    description: 'End date for published papers (YYYY-MM-DD)'
                }
            },
            required: ['query']
        }
    },
    {
        name: 'company_research_exa',
        description: 'Research companies using Exa AI - performs targeted searches of company websites to gather comprehensive information about businesses.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Company website URL (e.g., \'exa.ai\' or \'https://exa.ai\')'
                },
                subpageTarget: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Specific sections to target (e.g., [\'about\', \'pricing\', \'faq\', \'blog\']). If not provided, will crawl the most relevant pages.'
                },
                subpages: {
                    type: 'number',
                    description: 'Number of subpages to crawl (default: 10)',
                    default: 10
                }
            },
            required: ['query']
        }
    },
    {
        name: 'competitor_finder_exa',
        description: 'Find competitors of a company using Exa AI - performs targeted searches to identify businesses that offer similar products or services.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Describe what the company/product in a few words (e.g., \'web search API\', \'AI image generation\', \'cloud storage service\'). Keep it simple. Do not include the company name.'
                },
                excludeDomain: {
                    type: 'string',
                    description: 'Optional: The company\'s website to exclude from results (e.g., \'exa.ai\')'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of competitors to return (default: 10)',
                    default: 10
                }
            },
            required: ['query']
        }
    },
    {
        name: 'crawling_exa',
        description: 'Extract content from specific URLs using Exa AI - performs targeted crawling of web pages to retrieve their full content.',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'The URL to crawl (e.g., \'exa.ai\')'
                }
            },
            required: ['url']
        }
    },
    {
        name: 'linkedin_search_exa',
        description: 'Search LinkedIn for companies using Exa AI. Simply include company URL, or company name, with \'company page\' appended in your query.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query for LinkedIn (e.g., <url> company page OR <company name> company page)'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of search results to return (default: 5)',
                    default: 5
                }
            },
            required: ['query']
        }
    },
    {
        name: 'wikipedia_search_exa',
        description: 'Search Wikipedia using Exa AI - performs searches specifically within Wikipedia.org and returns relevant content from Wikipedia pages.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query for Wikipedia'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of search results to return (default: 5)',
                    default: 5
                }
            },
            required: ['query']
        }
    },
    {
        name: 'github_search_exa',
        description: 'Search GitHub repositories using Exa AI - performs real-time searches on GitHub.com to find relevant repositories and GitHub accounts.',
        inputSchema: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query for GitHub repositories, or Github account, or code'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of search results to return (default: 5)',
                    default: 5
                }
            },
            required: ['query']
        }
    },
    {
        name: 'exa_find_similar',
        description: 'Find similar content to a given URL using Exa AI',
        inputSchema: {
            type: 'object',
            properties: {
                url: {
                    type: 'string',
                    description: 'URL to find similar content for'
                },
                numResults: {
                    type: 'number',
                    description: 'Number of similar results to return (default: 5)',
                    default: 5
                },
                includeDomains: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Domains to include in search'
                },
                excludeDomains: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Domains to exclude from search'
                },
                excludeSourceDomain: {
                    type: 'boolean',
                    description: 'Exclude the source domain from results',
                    default: true
                }
            },
            required: ['url']
        }
    },
    {
        name: 'exa_get_contents',
        description: 'Get full content for specific Exa search result IDs',
        inputSchema: {
            type: 'object',
            properties: {
                ids: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Array of Exa result IDs to get content for'
                },
                text: {
                    type: 'boolean',
                    description: 'Include full text content',
                    default: true
                },
                highlights: {
                    type: 'boolean',
                    description: 'Include highlights',
                    default: false
                },
                summary: {
                    type: 'boolean',
                    description: 'Include AI-generated summary',
                    default: false
                }
            },
            required: ['ids']
        }
    }
];

// Helper functions for Exa integration
export class ExaMCPHelper {
    static validateApiKey(): boolean {
        return true; // API key is built-in
    }

    static formatSearchQuery(query: string, useAutoprompt: boolean = true): string {
        if (!useAutoprompt) {
            return query;
        }
        
        // Exa autoprompt enhancement
        if (query.length < 50 && !query.includes('site:') && !query.includes('"')) {
            return `Find comprehensive information about: ${query}`;
        }
        
        return query;
    }

    static formatResearchQuery(query: string): string {
        // Enhance query for academic research
        const academicKeywords = ['research', 'paper', 'study', 'analysis', 'academic', 'journal'];
        const hasAcademicKeywords = academicKeywords.some(keyword => 
            query.toLowerCase().includes(keyword)
        );
        
        if (!hasAcademicKeywords) {
            return `${query} research paper academic study`;
        }
        
        return query;
    }

    static extractDomain(url: string): string {
        try {
            const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
            return urlObj.hostname;
        } catch {
            return url;
        }
    }

    static buildSearchParams(params: ExaSearchParams): Record<string, any> {
        const searchParams: Record<string, any> = {
            query: params.query,
            numResults: params.numResults || 5,
            useAutoprompt: params.useAutoprompt !== false,
            type: params.type || 'neural'
        };

        if (params.includeDomains?.length) {
            searchParams.includeDomains = params.includeDomains;
        }

        if (params.excludeDomains?.length) {
            searchParams.excludeDomains = params.excludeDomains;
        }

        if (params.startCrawlDate) {
            searchParams.startCrawlDate = params.startCrawlDate;
        }

        if (params.endCrawlDate) {
            searchParams.endCrawlDate = params.endCrawlDate;
        }

        if (params.startPublishedDate) {
            searchParams.startPublishedDate = params.startPublishedDate;
        }

        if (params.endPublishedDate) {
            searchParams.endPublishedDate = params.endPublishedDate;
        }

        if (params.category) {
            searchParams.category = params.category;
        }

        if (params.includeText !== undefined) {
            searchParams.contents = {
                text: params.includeText,
                highlights: params.includeHighlights || false,
                summary: params.includeSummary || false
            };
        }

        return searchParams;
    }
}
