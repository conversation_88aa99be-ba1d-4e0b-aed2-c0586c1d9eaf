/**
 * Aizen AI Extension - Modern UI with Liquid Glass Design
 * Interactive TypeScript functionality for the webview
 */
class AizenUI {
    constructor() {
        this.state = {
            currentTab: 'actions',
            currentMode: 'auto',
            currentAgentMode: 'ask',
            autoMode: false
        };
        this.elements = {
            modeButton: null,
            modeDropdown: null,
            agentModeButton: null,
            agentModeDropdown: null,
            chatInput: null,
            sendButton: null,
            autoToggle: null,
            messagesContainer: null,
            chatMessages: null,
            tabButtons: null,
            modeOptions: null,
            agentModeOptions: null
        };
        this.initializeElements();
        this.setupEventListeners();
        this.setupMessageListener();
        this.initializeUI();
    }
    initializeElements() {
        // Tab elements
        this.elements.tabButtons = document.querySelectorAll('.tab-btn');
        // Mode selector elements
        this.elements.modeButton = document.getElementById('modeButton');
        this.elements.modeDropdown = document.getElementById('modeDropdown');
        this.elements.modeOptions = document.querySelectorAll('.mode-option');
        // Agent mode selector elements
        this.elements.agentModeButton = document.getElementById('agentModeButton');
        this.elements.agentModeDropdown = document.getElementById('agentModeDropdown');
        this.elements.agentModeOptions = document.querySelectorAll('.agent-mode-option');
        // Chat elements
        this.elements.chatInput = document.getElementById('chatInput');
        this.elements.sendButton = document.getElementById('sendButton');
        this.elements.autoToggle = document.getElementById('autoToggle');
        this.elements.messagesContainer = document.getElementById('messagesContainer');
        // Input elements
        this.elements.chatInput = document.getElementById('chatInput');
        this.elements.sendButton = document.getElementById('sendButton');
        this.elements.autoToggle = document.getElementById('autoToggle');
        // Chat elements
        this.elements.chatMessages = document.getElementById('chatMessages');
    }
    setupEventListeners() {
        // Tab switching
        this.elements.tabButtons?.forEach((btn) => {
            btn.addEventListener('click', (e) => {
                const target = e.target;
                const tab = target.dataset.tab;
                if (tab)
                    this.switchTab(tab);
            });
        });
        // Mode selector
        this.elements.modeButton?.addEventListener('click', () => {
            this.toggleDropdown('mode');
        });
        this.elements.modeOptions?.forEach((option) => {
            option.addEventListener('click', (e) => {
                const target = e.target;
                const mode = target.dataset.mode;
                if (mode)
                    this.selectMode(mode);
            });
        });
        // Agent mode selector
        this.elements.agentModeButton?.addEventListener('click', () => {
            this.toggleDropdown('agentMode');
        });
        this.elements.agentModeOptions?.forEach((option) => {
            option.addEventListener('click', (e) => {
                const target = e.target;
                const mode = target.dataset.mode;
                if (mode)
                    this.selectAgentMode(mode);
            });
        });
        // Chat input
        this.elements.chatInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        this.elements.sendButton?.addEventListener('click', () => {
            this.sendMessage();
        });
        // Auto toggle
        this.elements.autoToggle?.addEventListener('change', (e) => {
            const target = e.target;
            this.state.autoMode = target.checked;
            this.updateAutoMode();
        });
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            const target = e.target;
            if (!target.closest('.mode-selector')) {
                this.closeDropdown('mode');
            }
            if (!target.closest('.agent-mode-selector')) {
                this.closeDropdown('agentMode');
            }
        });
        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const target = e.target;
                const button = target.closest('.nav-btn');
                this.handleNavAction(button.title);
            });
        });
    }
    setupMessageListener() {
        window.addEventListener('message', (event) => {
            const message = event.data;
            switch (message.command) {
                case 'chatMessage':
                    this.addChatMessage(message.data);
                    break;
                case 'themeChanged':
                    this.handleThemeChange(message.theme);
                    break;
                case 'updateState':
                    this.updateUIState(message.data);
                    break;
            }
        });
    }
    initializeUI() {
        // Apply initial theme
        this.applyTheme();
        // Set initial states
        this.updateModeDisplay();
        this.updateAgentModeDisplay();
        // Add welcome animation
        setTimeout(() => {
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.classList.add('animate-in');
            }
        }, 300);
    }
    switchTab(tab) {
        this.state.currentTab = tab;
        // Update tab buttons
        this.elements.tabButtons?.forEach((btn) => {
            btn.classList.toggle('active', btn.dataset.tab === tab);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'tabChanged',
            data: { tab }
        });
        // Add visual feedback
        this.addRippleEffect(event?.target);
    }
    toggleDropdown(type) {
        const dropdown = type === 'mode' ? this.elements.modeDropdown : this.elements.agentModeDropdown;
        dropdown?.classList.toggle('show');
    }
    closeDropdown(type) {
        const dropdown = type === 'mode' ? this.elements.modeDropdown : this.elements.agentModeDropdown;
        dropdown?.classList.remove('show');
    }
    selectMode(mode) {
        this.state.currentMode = mode;
        this.updateModeDisplay();
        this.closeDropdown('mode');
        // Update active option
        this.elements.modeOptions?.forEach((option) => {
            option.classList.toggle('active', option.dataset.mode === mode);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'modeChanged',
            data: { mode }
        });
    }
    selectAgentMode(mode) {
        this.state.currentAgentMode = mode;
        this.updateAgentModeDisplay();
        this.closeDropdown('agentMode');
        // Update active option
        this.elements.agentModeOptions?.forEach((option) => {
            option.classList.toggle('active', option.dataset.mode === mode);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'agentModeChanged',
            data: { mode }
        });
    }
    updateModeDisplay() {
        const modeText = this.elements.modeButton?.querySelector('.mode-text');
        if (modeText) {
            modeText.textContent = this.state.currentMode.charAt(0).toUpperCase() + this.state.currentMode.slice(1);
        }
    }
    updateAgentModeDisplay() {
        const agentModeText = this.elements.agentModeButton?.querySelector('.agent-mode-text');
        if (agentModeText) {
            agentModeText.textContent = this.state.currentAgentMode.charAt(0).toUpperCase() + this.state.currentAgentMode.slice(1);
        }
    }
    updateAutoMode() {
        // Send message to extension
        vscode.postMessage({
            command: 'autoModeChanged',
            data: { autoMode: this.state.autoMode }
        });
    }
    sendMessage() {
        const input = this.elements.chatInput;
        const message = input?.value.trim();
        if (!message)
            return;
        // Clear input
        input.value = '';
        // Send message to extension
        vscode.postMessage({
            command: 'sendMessage',
            data: {
                message,
                mode: this.state.currentMode,
                agentMode: this.state.currentAgentMode,
                autoMode: this.state.autoMode
            }
        });
        // Add user message to chat immediately
        this.addChatMessage({
            type: 'user',
            message,
            timestamp: new Date().toISOString()
        });
    }
    addChatMessage(data) {
        const messagesContainer = this.elements.chatMessages;
        if (!messagesContainer)
            return;
        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${data.type} glass`;
        const time = new Date(data.timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${data.type === 'user' ? 'You' : 'Aizen AI'}</span>
                <span class="message-time">${time}</span>
            </div>
            <div class="message-content">${this.formatMessage(data.message)}</div>
        `;
        messagesContainer.appendChild(messageElement);
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        // Add animation
        setTimeout(() => {
            messageElement.classList.add('animate-in');
        }, 10);
    }
    formatMessage(message) {
        // Basic markdown-like formatting
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    handleNavAction(action) {
        // Handle specific navigation actions
        switch (action) {
            case 'New Chat':
                this.createNewChat();
                break;
            case 'Query History':
                this.showQueryHistory();
                break;
            case 'MCP Store':
                this.openMCPStore();
                break;
            case 'Advanced Command Mode':
                this.toggleAdvancedMode();
                break;
            default:
                // Send generic action to extension
                vscode.postMessage({
                    command: 'navAction',
                    data: { action }
                });
        }
    }
    createNewChat() {
        // Clear current chat
        const messagesContainer = this.elements.chatMessages;
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message glass">
                    <div class="welcome-icon">
                        <i class="hgi-stroke hgi-artificial-intelligence-02"></i>
                    </div>
                    <h2>Welcome to Aizen AI</h2>
                    <p>Your revolutionary AI assistant with advanced reasoning and swarm intelligence</p>
                    <div class="welcome-features">
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-brain"></i>
                            <span>Advanced Reasoning</span>
                        </div>
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-hierarchy-square-02"></i>
                            <span>Swarm Intelligence</span>
                        </div>
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-rocket-01"></i>
                            <span>Self-Improvement</span>
                        </div>
                    </div>
                </div>
            `;
        }
        // Clear input
        const input = this.elements.chatInput;
        if (input)
            input.value = '';
        // Send message to extension
        vscode.postMessage({
            command: 'newChat',
            data: {}
        });
        // Show notification
        this.showNotification('New chat started', 'success');
    }
    showQueryHistory() {
        vscode.postMessage({
            command: 'showHistory',
            data: {}
        });
        this.showNotification('Opening query history...', 'info');
    }
    openMCPStore() {
        vscode.postMessage({
            command: 'openMCPStore',
            data: {}
        });
        this.showNotification('Opening MCP Store...', 'info');
    }
    toggleAdvancedMode() {
        vscode.postMessage({
            command: 'toggleAdvancedMode',
            data: {}
        });
        this.showNotification('Advanced mode toggled', 'success');
    }
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type} glass`;
        notification.innerHTML = `
            <i class="hgi-stroke ${type === 'success' ? 'hgi-check-circle' : type === 'error' ? 'hgi-alert-circle' : 'hgi-information-circle'}"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(notification);
        // Animate in
        setTimeout(() => notification.classList.add('show'), 10);
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    handleThemeChange(theme) {
        document.documentElement.className = theme;
        this.applyTheme();
    }
    applyTheme() {
        // Apply theme-specific styles
        const isDark = document.documentElement.classList.contains('vscode-dark');
        const isHighContrast = document.documentElement.classList.contains('vscode-high-contrast');
        // Update CSS custom properties based on theme
        const root = document.documentElement;
        if (isHighContrast) {
            root.style.setProperty('--glass-primary', 'rgba(255, 255, 255, 0.1)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.3)');
        }
        else if (isDark) {
            root.style.setProperty('--glass-primary', 'rgba(255, 255, 255, 0.08)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.12)');
        }
        else {
            root.style.setProperty('--glass-primary', 'rgba(0, 0, 0, 0.05)');
            root.style.setProperty('--glass-border', 'rgba(0, 0, 0, 0.1)');
        }
    }
    updateUIState(data) {
        // Update UI state from extension
        Object.assign(this.state, data);
        this.updateModeDisplay();
        this.updateAgentModeDisplay();
    }
    addRippleEffect(element) {
        if (!element)
            return;
        const ripple = document.createElement('span');
        ripple.className = 'ripple';
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        setTimeout(() => ripple.remove(), 600);
    }
}
// Initialize the UI when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new AizenUI());
}
else {
    new AizenUI();
}
// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .chat-message {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease-out;
        margin-bottom: 1rem;
        padding: 1rem;
        border-radius: 0.75rem;
    }
    
    .chat-message.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .chat-message.user {
        margin-left: 2rem;
        background: var(--text-accent);
        color: white;
    }
    
    .chat-message.assistant {
        margin-right: 2rem;
        background: var(--glass-primary);
        color: var(--text-primary);
    }
    
    .chat-message.error {
        background: rgba(244, 67, 54, 0.1);
        border: 1px solid rgba(244, 67, 54, 0.3);
        color: #f44336;
    }
    
    .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        opacity: 0.8;
    }
    
    .message-sender {
        font-weight: 600;
    }
    
    .message-time {
        font-size: 0.75rem;
    }
    
    .message-content {
        line-height: 1.6;
    }
    
    .message-content code {
        background: rgba(0, 0, 0, 0.1);
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        font-size: 0.875rem;
    }

    /* Notification Styles */
    .notification {
        position: fixed;
        top: 1rem;
        right: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem 1.5rem;
        border-radius: 0.75rem;
        background: var(--glass-primary);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        color: var(--text-primary);
        font-size: 0.875rem;
        font-weight: 500;
        z-index: 1000;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s ease-out;
        max-width: 300px;
        box-shadow: var(--shadow-elevated);
    }

    .notification.show {
        transform: translateX(0);
        opacity: 1;
    }

    .notification.success {
        border-color: rgba(76, 175, 80, 0.3);
    }

    .notification.success i {
        color: #4caf50;
    }

    .notification.error {
        border-color: rgba(244, 67, 54, 0.3);
    }

    .notification.error i {
        color: #f44336;
    }

    .notification.info {
        border-color: rgba(33, 150, 243, 0.3);
    }

    .notification.info i {
        color: #2196f3;
    }

    .notification i {
        font-size: 1.2em;
        flex-shrink: 0;
    }
`;
document.head.appendChild(style);
//# sourceMappingURL=webview.js.map