"use strict";
/**
 * Aizen AI Extension - VS Code Entry Point
 * Connects React UI to Python AI backend with real AI frameworks
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const AizenExtensionManager_1 = require("./services/AizenExtensionManager");
const AizenIntegrationService_1 = require("./services/AizenIntegrationService");
const AizenChatViewProvider_1 = require("./providers/AizenChatViewProvider");
const hub_1 = require("./mcp/hub");
const security_1 = require("./mcp/security");
const exa_1 = require("./mcp/servers/exa");
const firecrawl_1 = require("./mcp/servers/firecrawl");
const test_1 = require("./mcp/test");
const MCPConfigEditorProvider_1 = require("./mcp/MCPConfigEditorProvider");
// Extension manager instance
let extensionManager;
let mcpHub;
let mcpSecurity;
let mcpConfigEditorProvider;
async function activate(context) {
    console.log('🚀 Activating Aizen AI Extension - Revolutionary AI Assistant');
    try {
        console.log('🔑 Built-in MCP servers will be configured with embedded API keys');
        // Initialize MCP Security Manager
        mcpSecurity = new security_1.MCPSecurityManager(context);
        // Initialize Integration Service
        const integrationService = new AizenIntegrationService_1.AizenIntegrationService();
        // Register Chat View Provider
        const chatViewProvider = new AizenChatViewProvider_1.AizenChatViewProvider(context.extensionUri, integrationService);
        context.subscriptions.push(vscode.window.registerWebviewViewProvider('aizen.chatView', chatViewProvider));
        console.log('✅ Chat view provider registered');
        // Initialize MCP Hub with new architecture
        mcpHub = new hub_1.MCPHub(context);
        mcpHub.initialize().then(async () => {
            console.log('✅ MCP Hub initialized successfully');
            // Auto-configure built-in servers
            await initializeBuiltInServers();
        }).catch((error) => {
            console.error('❌ MCP Hub initialization failed:', error);
        });
        // Initialize MCP Configuration Editor Provider
        mcpConfigEditorProvider = new MCPConfigEditorProvider_1.MCPConfigEditorProvider(context);
        console.log('✅ MCP Configuration Editor Provider initialized');
        // Register Settings command first
        context.subscriptions.push(vscode.commands.registerCommand('aizen.showSettings', async () => {
            console.log('🎯 Opening Aizen AI Settings...');
            try {
                // Import and use the AizenSettingsProvider
                const { AizenSettingsProvider } = await Promise.resolve().then(() => __importStar(require('./providers/AizenSettingsProvider')));
                AizenSettingsProvider.createOrShow(context.extensionUri, context, mcpHub);
            }
            catch (error) {
                console.error('❌ Failed to open settings:', error);
                vscode.window.showErrorMessage(`Failed to open settings: ${error}`);
            }
        }));
        // Register MCP commands
        context.subscriptions.push(vscode.commands.registerCommand('aizen.test.basic', () => {
            console.log('🎯 Basic test command executed!');
            vscode.window.showInformationMessage('🎯 Basic test works! MCP commands should work too.');
        }), vscode.commands.registerCommand('aizen.mcp.status', async () => {
            try {
                const servers = mcpHub.getServers();
                const connectedServers = mcpHub.getConnectedServers();
                const tools = mcpHub.getAllTools();
                const message = `🔌 MCP Status:\n` +
                    `• Total Servers: ${servers.length}\n` +
                    `• Connected: ${connectedServers.length}\n` +
                    `• Available Tools: ${tools.length}\n\n` +
                    `Connected Servers:\n${connectedServers.map(s => `  • ${s.config.name} (${s.tools?.length || 0} tools)`).join('\n')}`;
                vscode.window.showInformationMessage(message);
            }
            catch (error) {
                vscode.window.showErrorMessage(`MCP Status check failed: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.addExaServer', async () => {
            try {
                const apiKey = await vscode.window.showInputBox({
                    prompt: 'Enter your Exa API Key',
                    password: true,
                    placeHolder: 'exa_...'
                });
                if (apiKey) {
                    const config = {
                        ...exa_1.ExaMCPServerConfig,
                        env: {
                            ...exa_1.ExaMCPServerConfig.env,
                            'Authorization': `Bearer ${apiKey}`
                        }
                    };
                    const serverId = await mcpHub.addServer(config);
                    vscode.window.showInformationMessage(`✅ Exa server added successfully! Server ID: ${serverId}`);
                }
            }
            catch (error) {
                vscode.window.showErrorMessage(`Failed to add Exa server: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.addFirecrawlServer', async () => {
            try {
                const apiKey = await vscode.window.showInputBox({
                    prompt: 'Enter your Firecrawl API Key',
                    password: true,
                    placeHolder: 'fc-...'
                });
                if (apiKey) {
                    const config = {
                        ...firecrawl_1.FirecrawlMCPServerConfig,
                        env: {
                            ...firecrawl_1.FirecrawlMCPServerConfig.env,
                            'Authorization': `Bearer ${apiKey}`
                        }
                    };
                    const serverId = await mcpHub.addServer(config);
                    vscode.window.showInformationMessage(`✅ Firecrawl server added successfully! Server ID: ${serverId}`);
                }
            }
            catch (error) {
                vscode.window.showErrorMessage(`Failed to add Firecrawl server: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.testExaSearch', async () => {
            try {
                console.log('🧪 Testing Exa search...');
                const servers = mcpHub.getServers().filter(s => s.config.name.includes('Exa'));
                if (servers.length === 0) {
                    vscode.window.showWarningMessage('No Exa servers configured. Add one first.');
                    return;
                }
                const result = await mcpHub.executeTool(servers[0].id, 'web_search_exa', {
                    query: 'latest AI developments 2025',
                    numResults: 3
                });
                vscode.window.showInformationMessage(`✅ Exa search test successful! Found ${result.content?.length || 0} results.`);
            }
            catch (error) {
                console.error('❌ Exa test failed:', error);
                vscode.window.showErrorMessage(`❌ Exa test failed: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.testFirecrawlScrape', async () => {
            try {
                console.log('🧪 Testing Firecrawl scrape...');
                const servers = mcpHub.getServers().filter(s => s.config.name.includes('Firecrawl'));
                if (servers.length === 0) {
                    vscode.window.showWarningMessage('No Firecrawl servers configured. Add one first.');
                    return;
                }
                const result = await mcpHub.executeTool(servers[0].id, 'firecrawl_scrape', {
                    url: 'https://example.com',
                    formats: ['markdown']
                });
                const content = result.content?.[0];
                const contentLength = content?.type === 'text' ? content.text.length : 0;
                vscode.window.showInformationMessage(`✅ Firecrawl scrape test successful! Content length: ${contentLength} chars.`);
            }
            catch (error) {
                console.error('❌ Firecrawl test failed:', error);
                vscode.window.showErrorMessage(`❌ Firecrawl test failed: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.listTools', async () => {
            try {
                const tools = mcpHub.getAllTools();
                const toolsList = tools.map(t => `• ${t.tool.name} (${t.serverId})`).join('\n');
                const message = `� Available MCP Tools (${tools.length}):\n\n${toolsList || 'No tools available'}`;
                vscode.window.showInformationMessage(message);
            }
            catch (error) {
                vscode.window.showErrorMessage(`Failed to list tools: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.mcp.runTests', async () => {
            try {
                console.log('🧪 Running comprehensive MCP tests...');
                vscode.window.showInformationMessage('🧪 Running MCP tests... Check console for details.');
                await (0, test_1.runMCPTests)(context);
            }
            catch (error) {
                console.error('❌ MCP tests failed:', error);
                vscode.window.showErrorMessage(`MCP tests failed: ${error}`);
            }
        }), vscode.commands.registerCommand('aizen.test.simple', () => {
            console.log('🎯 Simple test command executed!');
            vscode.window.showInformationMessage('🎯 Simple test command works!');
        }), vscode.commands.registerCommand('aizen.mcp.configEditor', async () => {
            try {
                console.log('🔧 Opening MCP Configuration Editor...');
                await mcpConfigEditorProvider.openConfigEditor();
            }
            catch (error) {
                console.error('❌ Failed to open MCP Configuration Editor:', error);
                vscode.window.showErrorMessage(`Failed to open MCP Configuration Editor: ${error}`);
            }
        }));
        // Initialize Extension Manager
        extensionManager = AizenExtensionManager_1.AizenExtensionManager.getInstance(context);
        extensionManager.setMCPHub(mcpHub);
        await extensionManager.activate();
        console.log('✅ Aizen AI Extension activated successfully');
    }
    catch (error) {
        console.error('❌ Failed to activate Aizen AI Extension:', error);
        vscode.window.showErrorMessage(`Failed to activate Aizen AI: ${error instanceof Error ? error.message : String(error)}`);
    }
}
// Initialize built-in MCP servers
async function initializeBuiltInServers() {
    try {
        console.log('🔧 Initializing built-in MCP servers...');
        // Add Exa server with direct API key
        const exaConfig = {
            ...exa_1.ExaMCPServerConfig,
            enabled: true,
            autoStart: true,
            env: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer f01e507f-cdd2-454d-adcf-545d24035692`,
                'EXA_API_KEY': 'f01e507f-cdd2-454d-adcf-545d24035692'
            }
        };
        const exaServerId = await mcpHub.addServer(exaConfig);
        console.log('✅ Exa server initialized:', exaServerId);
        // Add Firecrawl server with direct API key
        const firecrawlConfig = {
            ...firecrawl_1.FirecrawlMCPServerConfig,
            enabled: true,
            autoStart: true,
            env: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer fc-73581888d5374a1a99893178925cc8bb`,
                'FIRECRAWL_API_KEY': 'fc-73581888d5374a1a99893178925cc8bb'
            }
        };
        const firecrawlServerId = await mcpHub.addServer(firecrawlConfig);
        console.log('✅ Firecrawl server initialized:', firecrawlServerId);
        vscode.window.showInformationMessage('🚀 Built-in MCP servers (Exa & Firecrawl) are ready!');
    }
    catch (error) {
        console.error('❌ Failed to initialize built-in servers:', error);
        vscode.window.showErrorMessage(`Failed to initialize MCP servers: ${error}`);
    }
}
async function deactivate() {
    console.log('🔄 Deactivating Aizen AI Extension...');
    if (extensionManager) {
        await extensionManager.deactivate();
    }
    if (mcpHub) {
        await mcpHub.dispose();
    }
    if (mcpSecurity) {
        await mcpSecurity.dispose();
    }
    console.log('✅ Aizen AI Extension deactivated');
}
//# sourceMappingURL=extension.js.map