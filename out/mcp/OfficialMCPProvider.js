"use strict";
/**
 * Aizen AI MCP Client - Integrates MCP servers directly into the extension
 * Uses the official MCP TypeScript SDK to connect to external MCP servers
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenMCPClient = void 0;
const vscode = __importStar(require("vscode"));
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
class AizenMCPClient {
    constructor(context) {
        this.clients = new Map();
        this.servers = [];
        this.tools = [];
        this.context = context;
        this.initializeBuiltinServers();
    }
    initializeBuiltinServers() {
        // Built-in servers with Aizen AI's API keys - users cannot modify these
        this.servers = [
            {
                id: 'exa-ai',
                name: 'Exa AI Search',
                command: 'npx',
                args: ['-y', 'exa-mcp-server'],
                env: {
                    'EXA_API_KEY': 'f01e507f-cdd2-454d-adcf-545d24035692'
                },
                enabled: true // Always enabled with built-in key
            },
            {
                id: 'firecrawl',
                name: 'Firecrawl',
                command: 'npx',
                args: ['-y', 'firecrawl-mcp'],
                env: {
                    'FIRECRAWL_API_KEY': 'fc-73581888d5374a1a99893178925cc8bb'
                },
                enabled: true // Always enabled with built-in key
            }
        ];
    }
    async initialize() {
        console.log('🔌 Initializing Aizen MCP Client with built-in servers...');
        // Connect to all built-in servers (they already have API keys)
        for (const server of this.servers) {
            if (server.enabled) {
                await this.connectToServer(server);
            }
        }
        console.log('✅ Aizen MCP Client initialized with built-in Exa and Firecrawl servers');
    }
    // Method removed - built-in servers are always enabled with hardcoded API keys
    async connectToServer(server) {
        try {
            console.log(`🔗 Connecting to MCP server: ${server.name}`);
            const client = new index_js_1.Client({
                name: 'aizen-ai-extension',
                version: '2.0.0'
            });
            const transport = new stdio_js_1.StdioClientTransport({
                command: server.command,
                args: server.args,
                env: { ...process.env, ...server.env }
            });
            await client.connect(transport);
            this.clients.set(server.id, client);
            // Load tools from this server
            await this.loadServerTools(server.id, client);
            console.log(`✅ Connected to ${server.name}`);
        }
        catch (error) {
            console.error(`❌ Failed to connect to ${server.name}:`, error);
            server.enabled = false;
        }
    }
    async loadServerTools(serverId, client) {
        try {
            const toolsResponse = await client.listTools();
            for (const tool of toolsResponse.tools) {
                this.tools.push({
                    name: tool.name,
                    description: tool.description || '',
                    inputSchema: tool.inputSchema,
                    serverId: serverId
                });
            }
            console.log(`📋 Loaded ${toolsResponse.tools.length} tools from ${serverId}`);
        }
        catch (error) {
            console.error(`❌ Failed to load tools from ${serverId}:`, error);
        }
    }
    async callTool(toolName, args) {
        const tool = this.tools.find(t => t.name === toolName);
        if (!tool) {
            throw new Error(`Tool '${toolName}' not found`);
        }
        const client = this.clients.get(tool.serverId);
        if (!client) {
            throw new Error(`Server '${tool.serverId}' not connected`);
        }
        try {
            console.log(`🔧 Calling tool: ${toolName} with args:`, args);
            const result = await client.callTool({
                name: toolName,
                arguments: args
            });
            console.log(`✅ Tool ${toolName} completed successfully`);
            return result;
        }
        catch (error) {
            console.error(`❌ Tool ${toolName} failed:`, error);
            throw error;
        }
    }
    getAvailableTools() {
        return [...this.tools];
    }
    getConnectedServers() {
        return this.servers.filter(s => s.enabled);
    }
    // API key methods removed - built-in servers use hardcoded keys
    async addExternalMCPServer() {
        const serverName = await vscode.window.showInputBox({
            prompt: 'Enter MCP Server Name',
            placeHolder: 'My Custom Server',
            ignoreFocusOut: true
        });
        if (!serverName)
            return;
        const command = await vscode.window.showInputBox({
            prompt: 'Enter MCP Server Command',
            placeHolder: 'npx my-mcp-server',
            ignoreFocusOut: true
        });
        if (!command)
            return;
        // Parse command and args
        const parts = command.split(' ');
        const cmd = parts[0];
        const args = parts.slice(1);
        // Add external server
        const externalServer = {
            id: `external-${Date.now()}`,
            name: serverName,
            command: cmd,
            args: args,
            env: {},
            enabled: true
        };
        this.servers.push(externalServer);
        await this.connectToServer(externalServer);
        vscode.window.showInformationMessage(`✅ External MCP server "${serverName}" added and connected!`);
    }
    async showMCPStatus() {
        const connectedServers = this.getConnectedServers();
        const availableTools = this.getAvailableTools();
        const builtinServers = connectedServers.filter(s => s.id === 'exa-ai' || s.id === 'firecrawl');
        const externalServers = connectedServers.filter(s => s.id !== 'exa-ai' && s.id !== 'firecrawl');
        let message = `🔌 Aizen MCP Status:\n` +
            `• Total Servers: ${connectedServers.length}\n` +
            `• Available Tools: ${availableTools.length}\n\n`;
        if (builtinServers.length > 0) {
            message += `Built-in Servers (Aizen AI):\n${builtinServers.map(s => `  ✅ ${s.name} (${this.tools.filter(t => t.serverId === s.id).length} tools)`).join('\n')}\n\n`;
        }
        if (externalServers.length > 0) {
            message += `External Servers:\n${externalServers.map(s => `  • ${s.name} (${this.tools.filter(t => t.serverId === s.id).length} tools)`).join('\n')}\n\n`;
        }
        if (availableTools.length > 0) {
            message += `Available Tools:\n${availableTools.slice(0, 10).map(t => `  • ${t.name} (${t.serverId})`).join('\n')}`;
            if (availableTools.length > 10) {
                message += `\n  ... and ${availableTools.length - 10} more tools`;
            }
        }
        vscode.window.showInformationMessage(message);
    }
    async dispose() {
        console.log('🔄 Disposing MCP clients...');
        for (const [serverId, client] of this.clients) {
            try {
                await client.close();
                console.log(`✅ Closed connection to ${serverId}`);
            }
            catch (error) {
                console.error(`❌ Error closing ${serverId}:`, error);
            }
        }
        this.clients.clear();
        this.tools = [];
        console.log('✅ MCP clients disposed');
    }
}
exports.AizenMCPClient = AizenMCPClient;
//# sourceMappingURL=OfficialMCPProvider.js.map