"use strict";
/**
 * MCP Configuration Service
 *
 * Bridges the configuration manager with the MCP client to provide:
 * - Hot-reloading of configuration changes
 * - Server lifecycle management based on configuration
 * - Security policy enforcement
 * - API key validation and management
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPConfigurationService = void 0;
const vscode = __importStar(require("vscode"));
const MCPConfigurationManager_1 = require("./MCPConfigurationManager");
const MCPSecurityValidator_1 = require("./MCPSecurityValidator");
class MCPConfigurationService {
    constructor(context, mcpClient, securityManager) {
        this.isInitialized = false;
        this.context = context;
        this.mcpClient = mcpClient;
        this.securityManager = securityManager;
        this.configManager = new MCPConfigurationManager_1.MCPConfigurationManager(context);
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        // Listen for configuration changes
        this.configManager.onConfigChanged(async (config) => {
            if (this.isInitialized) {
                await this.handleConfigurationChange(config);
            }
        });
    }
    async initialize() {
        try {
            console.log('🔧 Initializing MCP Configuration Service...');
            // Load current configuration
            const config = await this.configManager.loadConfiguration();
            // Apply global security settings
            await this.applyGlobalSecuritySettings(config);
            // Load external servers from configuration
            await this.loadExternalServers(config);
            this.isInitialized = true;
            console.log('✅ MCP Configuration Service initialized successfully');
        }
        catch (error) {
            console.error('❌ Failed to initialize MCP Configuration Service:', error);
            vscode.window.showErrorMessage(`Failed to initialize MCP configuration: ${error}`);
        }
    }
    async applyGlobalSecuritySettings(config) {
        if (!config.security)
            return;
        const securityPolicy = {};
        if (config.security.requireConfirmation !== undefined) {
            securityPolicy.requireConfirmation = config.security.requireConfirmation;
        }
        if (config.security.maxExecutionTime !== undefined) {
            securityPolicy.maxExecutionTime = config.security.maxExecutionTime;
        }
        if (config.security.allowedDomains) {
            securityPolicy.allowedDomains = config.security.allowedDomains;
        }
        if (config.security.blockedDomains) {
            securityPolicy.blockedDomains = config.security.blockedDomains;
        }
        if (config.security.allowedCommands) {
            securityPolicy.allowedTools = config.security.allowedCommands;
        }
        if (config.security.blockedCommands) {
            securityPolicy.blockedTools = config.security.blockedCommands;
        }
        await this.securityManager.updateSecurityPolicy(securityPolicy);
        console.log('🔒 Applied global security settings from configuration');
    }
    async loadExternalServers(config) {
        const externalServers = this.configManager.convertToExtendedServerConfigs(config);
        for (const serverConfig of externalServers) {
            try {
                // Validate server configuration
                await this.validateServerConfiguration(serverConfig, config);
                // Add server to MCP client
                await this.mcpClient.addExternalServer(serverConfig);
                console.log(`✅ Loaded external server: ${serverConfig.name}`);
            }
            catch (error) {
                console.error(`❌ Failed to load server ${serverConfig.name}:`, error);
                vscode.window.showWarningMessage(`Failed to load MCP server "${serverConfig.name}": ${error}`);
            }
        }
    }
    async validateServerConfiguration(serverConfig, globalConfig) {
        // Find the original server configuration
        const originalServer = globalConfig.servers.find(s => s.id === serverConfig.id);
        if (!originalServer) {
            throw new Error(`Server configuration not found: ${serverConfig.id}`);
        }
        // Use the comprehensive security validator
        const validationResult = MCPSecurityValidator_1.MCPSecurityValidator.validateServerConfiguration(originalServer, globalConfig);
        // Handle validation errors
        if (!validationResult.isValid) {
            const errorMessage = validationResult.errors.join('; ');
            throw new Error(`Security validation failed: ${errorMessage}`);
        }
        // Show warnings to user if any
        if (validationResult.warnings.length > 0) {
            const warningMessage = `Security warnings for server "${serverConfig.name}":\n${validationResult.warnings.join('\n')}`;
            console.warn(warningMessage);
            // Show warning notification for high-risk servers
            if (validationResult.riskLevel === 'high') {
                vscode.window.showWarningMessage(`High-risk MCP server detected: ${serverConfig.name}. Check the console for details.`, 'View Details').then(choice => {
                    if (choice === 'View Details') {
                        vscode.window.showInformationMessage(warningMessage);
                    }
                });
            }
        }
        // Check if API key is required and configured
        if (serverConfig.requiresApiKey && !serverConfig.apiKeyConfigured) {
            const envVar = originalServer.apiKey?.envVar;
            if (envVar) {
                throw new Error(`Missing API key: Environment variable ${envVar} is not set`);
            }
            else {
                throw new Error('API key is required but not configured');
            }
        }
        console.log(`✅ Security validation passed for server: ${serverConfig.name} (Risk: ${validationResult.riskLevel})`);
    }
    async handleConfigurationChange(config) {
        try {
            console.log('🔄 Configuration changed, reloading MCP servers...');
            // Apply updated security settings
            await this.applyGlobalSecuritySettings(config);
            // Get current external servers
            const currentServers = this.mcpClient.getConnectedServers().filter(s => !s.isBuiltIn);
            const newServerConfigs = this.configManager.convertToExtendedServerConfigs(config);
            // Remove servers that are no longer in configuration
            for (const currentServer of currentServers) {
                const stillExists = newServerConfigs.some(newServer => newServer.id === currentServer.id);
                if (!stillExists) {
                    try {
                        await this.mcpClient.removeServer(currentServer.id);
                        console.log(`🗑️ Removed server: ${currentServer.name}`);
                    }
                    catch (error) {
                        console.error(`Failed to remove server ${currentServer.name}:`, error);
                    }
                }
            }
            // Add or update servers
            for (const newServerConfig of newServerConfigs) {
                try {
                    const existingServer = currentServers.find(s => s.id === newServerConfig.id);
                    if (existingServer) {
                        // Update existing server (remove and re-add for simplicity)
                        await this.mcpClient.removeServer(existingServer.id);
                        await this.validateServerConfiguration(newServerConfig, config);
                        await this.mcpClient.addExternalServer(newServerConfig);
                        console.log(`🔄 Updated server: ${newServerConfig.name}`);
                    }
                    else {
                        // Add new server
                        await this.validateServerConfiguration(newServerConfig, config);
                        await this.mcpClient.addExternalServer(newServerConfig);
                        console.log(`➕ Added server: ${newServerConfig.name}`);
                    }
                }
                catch (error) {
                    console.error(`Failed to configure server ${newServerConfig.name}:`, error);
                    vscode.window.showWarningMessage(`Failed to configure MCP server "${newServerConfig.name}": ${error}`);
                }
            }
            vscode.window.showInformationMessage('MCP configuration reloaded successfully');
        }
        catch (error) {
            console.error('Error handling configuration change:', error);
            vscode.window.showErrorMessage(`Error reloading MCP configuration: ${error}`);
        }
    }
    async openConfigurationFile() {
        await this.configManager.openConfigurationFile();
    }
    async createDefaultConfiguration() {
        await this.configManager.createDefaultConfiguration();
    }
    async reloadConfiguration() {
        const config = await this.configManager.loadConfiguration();
        await this.handleConfigurationChange(config);
    }
    async validateCurrentConfiguration() {
        try {
            const config = await this.configManager.loadConfiguration();
            const errors = [];
            // Validate each server
            const serverConfigs = this.configManager.convertToExtendedServerConfigs(config);
            for (const serverConfig of serverConfigs) {
                try {
                    await this.validateServerConfiguration(serverConfig, config);
                }
                catch (error) {
                    errors.push(`Server "${serverConfig.name}": ${error}`);
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        }
        catch (error) {
            return {
                isValid: false,
                errors: [`Configuration file error: ${error}`]
            };
        }
    }
    getConfigurationPath() {
        return this.configManager.getConfigurationPath();
    }
    async exportConfiguration() {
        const config = await this.configManager.loadConfiguration();
        return JSON.stringify(config, null, 2);
    }
    async importConfiguration(configJson) {
        try {
            const config = JSON.parse(configJson);
            await this.configManager.saveConfiguration(config);
            vscode.window.showInformationMessage('MCP configuration imported successfully');
        }
        catch (error) {
            throw new Error(`Failed to import configuration: ${error}`);
        }
    }
    dispose() {
        this.configManager.dispose();
    }
}
exports.MCPConfigurationService = MCPConfigurationService;
//# sourceMappingURL=MCPConfigurationService.js.map