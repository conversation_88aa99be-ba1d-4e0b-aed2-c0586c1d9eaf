"use strict";
/**
 * MCP Security Validator
 *
 * Provides comprehensive security validation for user-defined MCP server configurations:
 * - Command injection prevention
 * - Path traversal protection
 * - URL validation and domain restrictions
 * - Environment variable sanitization
 * - Risk assessment and scoring
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPSecurityValidator = void 0;
const fs = __importStar(require("fs"));
class MCPSecurityValidator {
    static validateServerConfiguration(server, globalConfig) {
        const result = {
            isValid: true,
            riskLevel: 'low',
            warnings: [],
            errors: [],
            recommendations: []
        };
        // Validate based on transport type
        if (server.transport.type === 'stdio') {
            this.validateStdioTransport(server, globalConfig, result);
        }
        else if (['http', 'sse', 'streamable-http'].includes(server.transport.type)) {
            this.validateHttpTransport(server, globalConfig, result);
        }
        // Validate environment variables
        this.validateEnvironmentVariables(server, result);
        // Validate API key configuration
        this.validateApiKeyConfiguration(server, result);
        // Validate security settings
        this.validateSecuritySettings(server, result);
        // Calculate overall risk level
        this.calculateRiskLevel(result);
        // Generate recommendations
        this.generateRecommendations(server, result);
        return result;
    }
    static validateStdioTransport(server, globalConfig, result) {
        const command = server.transport.command;
        if (!command) {
            result.errors.push('STDIO transport requires a command');
            result.isValid = false;
            return;
        }
        // Check against dangerous commands
        const commandLower = command.toLowerCase();
        for (const dangerous of this.DANGEROUS_COMMANDS) {
            if (commandLower.includes(dangerous)) {
                result.errors.push(`Command contains dangerous keyword: ${dangerous}`);
                result.isValid = false;
            }
        }
        // Check against dangerous patterns
        const fullCommand = [command, ...(server.transport.args || [])].join(' ');
        for (const pattern of this.DANGEROUS_PATTERNS) {
            if (pattern.test(fullCommand)) {
                result.warnings.push(`Command contains potentially dangerous pattern: ${pattern.source}`);
            }
        }
        // Check against global security settings
        const security = globalConfig.security;
        if (security) {
            // Check blocked commands
            if (security.blockedCommands?.some(blocked => commandLower.includes(blocked.toLowerCase()))) {
                result.errors.push('Command is in the global blocked commands list');
                result.isValid = false;
            }
            // Check allowed commands (if allowlist exists)
            if (security.allowedCommands && security.allowedCommands.length > 0) {
                const isAllowed = security.allowedCommands.some(allowed => commandLower.startsWith(allowed.toLowerCase()));
                if (!isAllowed) {
                    result.errors.push('Command is not in the global allowed commands list');
                    result.isValid = false;
                }
            }
        }
        // Validate working directory
        if (server.transport.cwd) {
            this.validateWorkingDirectory(server.transport.cwd, result);
        }
        // Check if command is in safe list
        const isSafe = this.SAFE_COMMANDS.some(safe => commandLower.startsWith(safe));
        if (!isSafe) {
            result.warnings.push('Command is not in the known safe commands list');
        }
    }
    static validateHttpTransport(server, globalConfig, result) {
        const url = server.transport.url;
        if (!url) {
            result.errors.push('HTTP transport requires a URL');
            result.isValid = false;
            return;
        }
        try {
            const urlObj = new URL(url);
            // Validate protocol
            if (!['http:', 'https:'].includes(urlObj.protocol)) {
                result.errors.push(`Unsupported protocol: ${urlObj.protocol}`);
                result.isValid = false;
            }
            // Require HTTPS for external domains
            if (urlObj.protocol === 'http:' && !this.isLocalDomain(urlObj.hostname)) {
                result.warnings.push('HTTP is not secure for external domains. Consider using HTTPS.');
            }
            // Check against blocked domains
            const hostname = urlObj.hostname.toLowerCase();
            for (const blocked of this.BLOCKED_DOMAINS) {
                if (hostname.includes(blocked)) {
                    result.errors.push(`Domain is blocked: ${blocked}`);
                    result.isValid = false;
                }
            }
            // Check against global security settings
            const security = globalConfig.security;
            if (security) {
                // Check blocked domains
                if (security.blockedDomains?.some(blocked => hostname.includes(blocked.toLowerCase()))) {
                    result.errors.push('Domain is in the global blocked domains list');
                    result.isValid = false;
                }
                // Check allowed domains (if allowlist exists)
                if (security.allowedDomains && security.allowedDomains.length > 0) {
                    const isAllowed = security.allowedDomains.some(allowed => hostname.includes(allowed.toLowerCase()));
                    if (!isAllowed) {
                        result.errors.push('Domain is not in the global allowed domains list');
                        result.isValid = false;
                    }
                }
            }
            // Validate headers
            if (server.transport.headers) {
                this.validateHttpHeaders(server.transport.headers, result);
            }
        }
        catch (error) {
            result.errors.push(`Invalid URL: ${error}`);
            result.isValid = false;
        }
    }
    static validateEnvironmentVariables(server, result) {
        const env = server.transport.env;
        if (!env)
            return;
        for (const [key, value] of Object.entries(env)) {
            // Validate environment variable names
            if (!/^[A-Z_][A-Z0-9_]*$/.test(key)) {
                result.warnings.push(`Environment variable name should follow convention: ${key}`);
            }
            // Check for sensitive patterns in values
            if (value.includes('${') || value.includes('$(') || value.includes('`')) {
                result.warnings.push(`Environment variable ${key} contains potentially dangerous substitution`);
            }
            // Check for hardcoded secrets (basic patterns)
            if (this.looksLikeSecret(value) && !value.startsWith('${')) {
                result.warnings.push(`Environment variable ${key} appears to contain a hardcoded secret`);
            }
        }
    }
    static validateApiKeyConfiguration(server, result) {
        if (!server.apiKey?.required)
            return;
        if (!server.apiKey.envVar) {
            result.warnings.push('API key is required but no environment variable specified');
        }
        else if (!/^[A-Z_][A-Z0-9_]*$/.test(server.apiKey.envVar)) {
            result.warnings.push('API key environment variable name should follow convention');
        }
    }
    static validateSecuritySettings(server, result) {
        const security = server.security;
        if (!security)
            return;
        // Validate risk level
        if (security.riskLevel && !['low', 'medium', 'high'].includes(security.riskLevel)) {
            result.errors.push(`Invalid risk level: ${security.riskLevel}`);
            result.isValid = false;
        }
        // Validate execution time limits
        if (security.maxExecutionTime) {
            if (security.maxExecutionTime < 1000 || security.maxExecutionTime > 600000) {
                result.warnings.push('Max execution time should be between 1 second and 10 minutes');
            }
        }
    }
    static validateWorkingDirectory(cwd, result) {
        // Check for path traversal
        if (cwd.includes('..')) {
            result.errors.push('Working directory contains path traversal (..)');
            result.isValid = false;
        }
        // Check for system directories
        const systemDirs = ['/etc', '/sys', '/proc', '/dev', '/boot', '/root'];
        if (systemDirs.some(dir => cwd.startsWith(dir))) {
            result.errors.push('Working directory points to system directory');
            result.isValid = false;
        }
        // Check if directory exists (warning only)
        try {
            if (!fs.existsSync(cwd)) {
                result.warnings.push('Working directory does not exist');
            }
        }
        catch (error) {
            result.warnings.push('Cannot verify working directory existence');
        }
    }
    static validateHttpHeaders(headers, result) {
        for (const [key, value] of Object.entries(headers)) {
            // Check for dangerous headers
            const keyLower = key.toLowerCase();
            if (['host', 'origin', 'referer'].includes(keyLower)) {
                result.warnings.push(`Header ${key} can be dangerous if not properly validated`);
            }
            // Check for potential secrets in headers
            if (this.looksLikeSecret(value) && !value.startsWith('${')) {
                result.warnings.push(`Header ${key} appears to contain a hardcoded secret`);
            }
        }
    }
    static calculateRiskLevel(result) {
        let riskScore = 0;
        // Errors increase risk significantly
        riskScore += result.errors.length * 10;
        // Warnings increase risk moderately
        riskScore += result.warnings.length * 3;
        if (riskScore >= 20) {
            result.riskLevel = 'high';
        }
        else if (riskScore >= 10) {
            result.riskLevel = 'medium';
        }
        else {
            result.riskLevel = 'low';
        }
    }
    static generateRecommendations(server, result) {
        // General recommendations
        if (server.transport.type === 'stdio') {
            result.recommendations.push('Consider using specific version pinning for npm packages (e.g., @package@1.2.3)');
            if (!server.transport.cwd) {
                result.recommendations.push('Specify a working directory to limit file system access');
            }
        }
        if (['http', 'sse', 'streamable-http'].includes(server.transport.type)) {
            const url = server.transport.url;
            if (url && new URL(url).protocol === 'http:') {
                result.recommendations.push('Use HTTPS instead of HTTP for better security');
            }
        }
        if (server.apiKey?.required && !server.apiKey.envVar) {
            result.recommendations.push('Use environment variables for API keys instead of hardcoding them');
        }
        if (!server.security?.riskLevel) {
            result.recommendations.push('Specify a risk level for better security assessment');
        }
        if (result.riskLevel === 'high') {
            result.recommendations.push('Consider reviewing and reducing the security risks before enabling this server');
        }
    }
    static isLocalDomain(hostname) {
        return ['localhost', '127.0.0.1', '::1'].includes(hostname) ||
            hostname.startsWith('192.168.') ||
            hostname.startsWith('10.') ||
            hostname.startsWith('172.');
    }
    static looksLikeSecret(value) {
        // Basic heuristics for detecting secrets
        if (value.length < 8)
            return false;
        // Check for common secret patterns
        const secretPatterns = [
            /^[A-Za-z0-9+/]{20,}={0,2}$/, // Base64-like
            /^[a-f0-9]{32,}$/i, // Hex strings
            /^[A-Z0-9_]{20,}$/, // API key pattern
            /^sk-[a-zA-Z0-9]{20,}$/, // OpenAI-style keys
            /^xoxb-[a-zA-Z0-9-]{20,}$/, // Slack bot tokens
            /^ghp_[a-zA-Z0-9]{36}$/, // GitHub personal access tokens
        ];
        return secretPatterns.some(pattern => pattern.test(value));
    }
}
exports.MCPSecurityValidator = MCPSecurityValidator;
MCPSecurityValidator.DANGEROUS_COMMANDS = [
    'rm', 'del', 'format', 'sudo', 'su', 'chmod', 'chown',
    'kill', 'killall', 'pkill', 'shutdown', 'reboot',
    'dd', 'fdisk', 'mkfs', 'mount', 'umount',
    'iptables', 'ufw', 'firewall-cmd',
    'curl', 'wget', 'nc', 'netcat', 'telnet'
];
MCPSecurityValidator.SAFE_COMMANDS = [
    'node', 'npm', 'npx', 'yarn', 'pnpm',
    'python', 'python3', 'pip', 'pip3',
    'java', 'javac', 'mvn', 'gradle',
    'go', 'cargo', 'rustc',
    'git', 'docker', 'kubectl'
];
MCPSecurityValidator.DANGEROUS_PATTERNS = [
    /\$\([^)]*\)/g, // Command substitution $(...)
    /`[^`]*`/g, // Command substitution `...`
    /&&|;|\|\|/g, // Command chaining
    />\s*\/dev\/null/g, // Output redirection
    /2>&1/g, // Error redirection
    /\|\s*sh/g, // Pipe to shell
    /eval\s*\(/g, // Eval function
    /exec\s*\(/g, // Exec function
    /system\s*\(/g, // System function
    /\.\.\//g, // Path traversal
    /\/etc\/passwd/g, // System files
    /\/etc\/shadow/g, // System files
    /\/proc\//g, // Process filesystem
    /\/sys\//g, // System filesystem
];
MCPSecurityValidator.BLOCKED_DOMAINS = [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '::1',
    'metadata.google.internal',
    '***************', // AWS metadata
    '***************', // Alibaba metadata
    '192.168.', // Private networks
    '10.', // Private networks
    '172.16.', // Private networks
    '172.17.', // Private networks
    '172.18.', // Private networks
    '172.19.', // Private networks
    '172.20.', // Private networks
    '172.21.', // Private networks
    '172.22.', // Private networks
    '172.23.', // Private networks
    '172.24.', // Private networks
    '172.25.', // Private networks
    '172.26.', // Private networks
    '172.27.', // Private networks
    '172.28.', // Private networks
    '172.29.', // Private networks
    '172.30.', // Private networks
    '172.31.' // Private networks
];
//# sourceMappingURL=MCPSecurityValidator.js.map