{"version": 3, "file": "MCPConfigEditorProvider.js", "sourceRoot": "", "sources": ["../../src/mcp/MCPConfigEditorProvider.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AA6C7B,MAAa,uBAAuB;IAKhC,YAAY,OAAgC;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;IAC3C,CAAC;IAEO,aAAa;QACjB,8DAA8D;QAC9D,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAC5E,CAAC;QAED,kCAAkC;QAClC,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACpC,EAAE,CAAC,SAAS,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACzC,iBAAiB,EACjB,0BAA0B,EAC1B,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACtE;SACJ,CACJ,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzD,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,wBAAwB,CAAC,CAAC;QAC9F,IAAI,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAE7C,sCAAsC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC;QAC3G,MAAM,KAAK,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAEvD,IAAI,GAAG,IAAI,CAAC,OAAO,CACf,8CAA8C,EAC9C,gBAAgB,KAAK,aAAa,CACrC,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,2BAA2B;QAC/B,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACtD,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,mBAAmB;oBACpB,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;oBACtC,MAAM;gBAEV,KAAK,mBAAmB;oBACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC7C,MAAM;gBAEV,KAAK,uBAAuB;oBACxB,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACjD,MAAM;gBAEV;oBACI,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,IAAI,CAAC;YACD,IAAI,MAAyB,CAAC;YAE9B,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACjC,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;gBACzD,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACJ,+BAA+B;gBAC/B,MAAM,GAAG;oBACL,UAAU,EAAE,EAAE;oBACd,cAAc,EAAE;wBACZ,SAAS,EAAE,IAAI;wBACf,oBAAoB,EAAE,EAAE;wBACxB,cAAc,EAAE,KAAK;wBACrB,QAAQ,EAAE,MAAM;qBACnB;oBACD,QAAQ,EAAE;wBACN,mBAAmB,EAAE,IAAI;wBACzB,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;wBACjD,eAAe,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC;wBAChD,gBAAgB,EAAE,KAAK;qBAC1B;oBACD,QAAQ,EAAE;wBACN,IAAI,EAAE,4BAA4B;wBAClC,WAAW,EAAE,uCAAuC;wBACpD,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBACjC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACzC;iBACJ,CAAC;YACN,CAAC;YAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,qBAAqB;gBAC3B,MAAM,EAAE,MAAM;aACjB,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,MAAyB;QACrD,IAAI,CAAC;YACD,kBAAkB;YAClB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5D,CAAC;YAED,uCAAuC;YACvC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAEvC,0BAA0B;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,6CAA6C;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAChD,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEnD,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,oBAAoB;aAC7B,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,MAAyB;QACzD,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,MAAM,EAAE;oBACJ,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,EAAE;oBACV,QAAQ,EAAE,EAAE;iBACf;aACJ,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5B,IAAI,EAAE,kBAAkB;gBACxB,MAAM,EAAE;oBACJ,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC;oBACvB,QAAQ,EAAE,EAAE;iBACf;aACJ,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,MAAyB;QACvD,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACpD,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAChD,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QACvD,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,QAAgB,EAAE,MAA+B;QAC1E,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,wEAAwE,CAAC,CAAC;QAC5H,CAAC;QAED,gCAAgC;QAChC,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;QAClC,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC;QAEnD,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,0EAA0E,CAAC,CAAC;QAClH,CAAC;QAED,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,4DAA4D,CAAC,CAAC;QACpG,CAAC;QAED,2BAA2B;QAC3B,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxD,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,2BAA2B,CAAC,CAAC;YACnE,CAAC;YAED,iCAAiC;YACjC,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACb,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;oBACpD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;wBACnC,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,wCAAwC,GAAG,EAAE,CAAC,CAAC;oBACrF,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC;YAC3C,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,2CAA2C,CAAC,CAAC;YACnF,CAAC;YAED,IAAI,CAAC;gBACD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;YAAC,MAAM,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,yBAAyB,GAAG,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,mBAAmB;YACnB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC;wBAChC,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,0BAA0B,GAAG,EAAE,CAAC,CAAC;oBACvE,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,UAAU,QAAQ,gCAAgC,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,QAA0D;QACrF,IAAI,QAAQ,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,OAAO,QAAQ,CAAC,oBAAoB,KAAK,QAAQ;gBACjD,QAAQ,CAAC,oBAAoB,GAAG,CAAC;gBACjC,QAAQ,CAAC,oBAAoB,GAAG,EAAE,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;YAC9E,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,OAAO,QAAQ,CAAC,cAAc,KAAK,QAAQ;gBAC3C,QAAQ,CAAC,cAAc,GAAG,IAAI;gBAC9B,QAAQ,CAAC,cAAc,GAAG,MAAM,EAAE,CAAC;gBACnC,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;YAC5F,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;YACzE,CAAC;QACL,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,QAAoD;QACjF,IAAI,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YAC1C,IAAI,OAAO,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;gBAC7C,QAAQ,CAAC,gBAAgB,GAAG,IAAI;gBAChC,QAAQ,CAAC,gBAAgB,GAAG,MAAM,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;YAC9F,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,QAAQ,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,QAAQ,CAAC,cAAc,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACrE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED,oBAAoB;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;IACL,CAAC;CACJ;AAjVD,0DAiVC"}