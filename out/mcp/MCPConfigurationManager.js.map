{"version": 3, "file": "MCPConfigurationManager.js", "sourceRoot": "", "sources": ["../../src/mcp/MCPConfigurationManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAgF7B,MAAa,uBAAuB;IAQhC,YAAY,OAAgC;QAHpC,2BAAsB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAkB,CAAC;QAC3D,oBAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;QAGhE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,EAAE,uBAAuB,CAAC,CAAC;QACvF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAEO,aAAa;QACjB,8DAA8D;QAC9D,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClD,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACvE,CAAC;QAED,kCAAkC;QAClC,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;QAC/D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACpC,EAAE,CAAC,SAAS,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;IAC1D,CAAC;IAEO,gBAAgB;QACpB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CACvD,IAAI,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAC1D,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;YAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;YAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,EAAE;YAC9B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;QACxE,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,sBAAsB;QAChC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC9C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAEO,sBAAsB;QAC1B,gDAAgD;QAChD,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEnD,+DAA+D;QAC/D,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,CAC5C,SAAS,EACT;YACI;gBACI,SAAS,EAAE,CAAC,mBAAmB,CAAC;gBAChC,GAAG,EAAE,SAAS,CAAC,QAAQ,EAAE;aAC5B;SACJ,EACD,MAAM,CAAC,mBAAmB,CAAC,MAAM,CACpC,CAAC;IACN,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC5B,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACjD,qEAAqE,EACrE,WAAW,EACX,QAAQ,CACX,CAAC;YAEF,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;gBACzB,OAAO;YACX,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAmB;YAClC,OAAO,EAAE,iCAAiC;YAC1C,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE;gBACN,IAAI,EAAE,gBAAgB;gBACtB,WAAW,EAAE,iDAAiD;gBAC9D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACjC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACzC;YACD,cAAc,EAAE;gBACZ,SAAS,EAAE,IAAI;gBACf,oBAAoB,EAAE,EAAE;gBACxB,cAAc,EAAE,KAAK;gBACrB,aAAa,EAAE,CAAC;gBAChB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,MAAM;aACnB;YACD,QAAQ,EAAE;gBACN,mBAAmB,EAAE,IAAI;gBACzB,eAAe,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC;gBACrD,eAAe,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC;gBAChD,gBAAgB,EAAE,KAAK;gBACvB,WAAW,EAAE,KAAK;aACrB;YACD,OAAO,EAAE;gBACL;oBACI,EAAE,EAAE,gBAAgB;oBACpB,IAAI,EAAE,oBAAoB;oBAC1B,WAAW,EAAE,6DAA6D;oBAC1E,IAAI,EAAE,IAAI;oBACV,QAAQ,EAAE,OAAO;oBACjB,SAAS,EAAE;wBACP,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,CAAC,IAAI,EAAE,sCAAsC,CAAC;qBACvD;oBACD,QAAQ,EAAE;wBACN,OAAO,EAAE,KAAK;wBACd,SAAS,EAAE,KAAK;wBAChB,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,EAAE;qBACf;oBACD,QAAQ,EAAE;wBACN,SAAS,EAAE,QAAQ;wBACnB,mBAAmB,EAAE,IAAI;qBAC5B;oBACD,QAAQ,EAAE;wBACN,OAAO,EAAE,OAAO;wBAChB,MAAM,EAAE,gBAAgB;wBACxB,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;qBAChC;iBACJ;aACJ;SACJ,CAAC;QAEF,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;YAC7C,CAAC;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAmB,CAAC;YAErD,yBAAyB;YACzB,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEnC,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAsB;QAC1C,IAAI,CAAC;YACD,gCAAgC;YAChC,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,MAAM,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5D,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;YAEnC,0BAA0B;YAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,6CAA6C;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAChD,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEnD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,MAAsB;QAChD,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC/D,CAAC;QAED,uBAAuB;QACvB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAU,CAAC;QACpC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YAElC,0BAA0B;YAC1B,IAAI,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACzD,CAAC;YACD,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,MAA4B;QACrD,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,KAAK,CAAC,sBAAsB,MAAM,CAAC,EAAE,wEAAwE,CAAC,CAAC;QAC7H,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,EAAE,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YAC9C,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,EAAE,oCAAoC,CAAC,CAAC;QAC7E,CAAC;QAED,2CAA2C;QAC3C,IAAI,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,gBAAgB,MAAM,CAAC,EAAE,sBAAsB,CAAC,CAAC;YACrE,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5E,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,eAAe,MAAM,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC;gBACD,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAClC,CAAC;YAAC,MAAM,CAAC;gBACL,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,EAAE,qBAAqB,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;YACpF,CAAC;QACL,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/F,MAAM,IAAI,KAAK,CAAC,UAAU,MAAM,CAAC,EAAE,4BAA4B,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;QAChG,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,QAAiD;QAC5E,IAAI,QAAQ,CAAC,gBAAgB,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,IAAI,QAAQ,CAAC,gBAAgB,GAAG,MAAM,CAAC,EAAE,CAAC;YACxG,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;QACrF,CAAC;QAED,yBAAyB;QACzB,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC3B,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACzC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;QACL,CAAC;QAED,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC3B,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACzC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrD,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB;QACvB,IAAI,CAAC;YACD,mDAAmD;YACnD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAC5C,CAAC;YAED,2BAA2B;YAC3B,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC1E,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE;gBAC3C,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG;aACpC,CAAC,CAAC;YAEH,uBAAuB;YACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,yFAAyF,EACzF,oBAAoB,CACvB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACZ,IAAI,MAAM,KAAK,oBAAoB,EAAE,CAAC;oBAClC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC,CAAC;gBACzF,CAAC;YACL,CAAC,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;QACjF,CAAC;IACL,CAAC;IAED,8BAA8B,CAAC,MAAsB;QACjD,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAClF,CAAC;IAEO,mBAAmB,CAAC,MAA4B,EAAE,YAA4B;QAClF,MAAM,cAAc,GAAG,YAAY,CAAC,cAAc,IAAI,EAAE,CAAC;QACzD,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,IAAI,EAAE,CAAC;QAEnD,OAAO;YACH,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW,IAAI,EAAE;YACrC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;YAChC,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,OAAO;YACjC,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI;YAC3B,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG;YACzB,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG;YACzB,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI;YACzC,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS,IAAI,cAAc,CAAC,SAAS,IAAI,IAAI;YACzE,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,IAAI,cAAc,CAAC,cAAc,IAAI,KAAK;YAC3E,aAAa,EAAE,MAAM,CAAC,QAAQ,EAAE,aAAa,IAAI,cAAc,CAAC,aAAa,IAAI,CAAC;YAClF,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,UAAU,IAAI,cAAc,CAAC,UAAU,IAAI,IAAI;YAC5E,SAAS,EAAE,KAAK;YAChB,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,IAAI,KAAK;YAChD,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI;YACnG,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,OAAO;YACpC,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI;YACzB,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO;YACjC,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,SAAS,IAAI,QAAQ;YACjD,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE;SAC5C,CAAC;IACN,CAAC;IAEO,qBAAqB,CAAC,MAAe;QACzC,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QACzB,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAED,oBAAoB;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC;CACJ;AAzWD,0DAyWC"}