"use strict";
/**
 * Aizen AI MCP Client - Comprehensive Model Context Protocol Implementation
 *
 * This is a complete rewrite of the MCP client with the following improvements:
 * - Support for multiple transport types (STDIO, HTTP, Streamable HTTP)
 * - Built-in security and user consent management
 * - Proper error handling and recovery
 * - Session management and connection lifecycle
 * - Support for both built-in and external MCP servers
 * - Real-time status monitoring and logging
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AizenMCPClient = void 0;
const vscode = __importStar(require("vscode"));
const events_1 = require("events");
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const sse_js_1 = require("@modelcontextprotocol/sdk/client/sse.js");
const streamableHttp_js_1 = require("@modelcontextprotocol/sdk/client/streamableHttp.js");
const types_1 = require("./types");
class AizenMCPClient extends events_1.EventEmitter {
    constructor(options) {
        super();
        this.clients = new Map();
        this.servers = new Map();
        this.serverStatus = new Map();
        this.tools = new Map();
        this.resources = new Map();
        this.prompts = new Map();
        this.userConsents = new Map();
        this.activityLogs = [];
        this.isInitialized = false;
        this.context = options.context;
        this.connectionTimeout = options.connectionTimeout || 30000;
        this.maxConcurrentConnections = options.maxConcurrentConnections || 10;
        this.debugMode = options.debugMode || false;
        // Default security policy
        this.securityPolicy = options.securityPolicy || {
            requireConfirmation: true,
            maxExecutionTime: 60000,
            userConsentRequired: true,
            logAllActivities: true,
            riskAssessment: true
        };
        this.initializeBuiltinServers();
    }
    initializeBuiltinServers() {
        // Built-in Exa AI server with pre-configured API key
        const exaServer = {
            id: 'exa-ai',
            name: 'Exa AI Search',
            description: 'Advanced web search and research capabilities powered by Exa AI',
            transport: 'stdio',
            command: 'npx',
            args: ['-y', '@exa-ai/mcp-server'],
            env: {
                'EXA_API_KEY': 'f01e507f-cdd2-454d-adcf-545d24035692'
            },
            enabled: true,
            autoStart: true,
            timeout: 30000,
            retryAttempts: 3,
            retryDelay: 1000,
            isBuiltIn: true,
            requiresApiKey: false, // Pre-configured
            apiKeyConfigured: true,
            category: 'search',
            icon: '🔍'
        };
        // Built-in Firecrawl server with pre-configured API key
        const firecrawlServer = {
            id: 'firecrawl',
            name: 'Firecrawl',
            description: 'Web scraping and content extraction capabilities',
            transport: 'stdio',
            command: 'npx',
            args: ['-y', '@firecrawl/mcp-server'],
            env: {
                'FIRECRAWL_API_KEY': 'fc-73581888d5374a1a99893178925cc8bb'
            },
            enabled: true,
            autoStart: true,
            timeout: 60000,
            retryAttempts: 3,
            retryDelay: 2000,
            isBuiltIn: true,
            requiresApiKey: false, // Pre-configured
            apiKeyConfigured: true,
            category: 'scraping',
            icon: '🕷️'
        };
        this.servers.set(exaServer.id, exaServer);
        this.servers.set(firecrawlServer.id, firecrawlServer);
        // Initialize status for built-in servers
        this.serverStatus.set(exaServer.id, {
            id: exaServer.id,
            name: exaServer.name,
            status: types_1.MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: true
        });
        this.serverStatus.set(firecrawlServer.id, {
            id: firecrawlServer.id,
            name: firecrawlServer.name,
            status: types_1.MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: true
        });
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        this.log('info', '🔌 Initializing Aizen MCP Client...');
        try {
            // Load saved configuration
            await this.loadConfiguration();
            // Connect to enabled servers
            const enabledServers = Array.from(this.servers.values()).filter(s => s.enabled);
            for (const server of enabledServers) {
                if (this.clients.size >= this.maxConcurrentConnections) {
                    this.log('warn', `Maximum concurrent connections (${this.maxConcurrentConnections}) reached. Skipping ${server.name}`);
                    continue;
                }
                try {
                    await this.connectToServer(server);
                }
                catch (error) {
                    this.log('error', `Failed to connect to ${server.name}:`, error);
                }
            }
            this.isInitialized = true;
            this.log('info', '✅ Aizen MCP Client initialized successfully');
            this.emit('initialized', {
                connectedServers: this.clients.size,
                totalTools: this.tools.size,
                totalResources: this.resources.size,
                totalPrompts: this.prompts.size
            });
        }
        catch (error) {
            this.log('error', '❌ Failed to initialize MCP Client:', error);
            throw new types_1.MCPError('Failed to initialize MCP Client', 'INITIALIZATION_ERROR', undefined, error);
        }
    }
    async loadConfiguration() {
        try {
            const config = this.context.globalState.get('mcpConfiguration');
            if (config) {
                // Load external servers from configuration
                if (config.servers) {
                    for (const serverConfig of config.servers) {
                        if (!serverConfig.isBuiltIn) {
                            this.servers.set(serverConfig.id, serverConfig);
                            this.serverStatus.set(serverConfig.id, {
                                id: serverConfig.id,
                                name: serverConfig.name,
                                status: types_1.MCPConnectionStatus.Disconnected,
                                toolCount: 0,
                                resourceCount: 0,
                                promptCount: 0,
                                isBuiltIn: false
                            });
                        }
                    }
                }
                // Load security policy
                if (config.security) {
                    this.securityPolicy = { ...this.securityPolicy, ...config.security };
                }
                // Load user consents
                if (config.userConsents) {
                    for (const consent of config.userConsents) {
                        this.userConsents.set(`${consent.serverId}:${consent.toolName}`, consent);
                    }
                }
            }
        }
        catch (error) {
            this.log('warn', 'Failed to load configuration, using defaults:', error);
        }
    }
    async saveConfiguration() {
        try {
            const config = {
                servers: Array.from(this.servers.values()),
                security: this.securityPolicy,
                userConsents: Array.from(this.userConsents.values()),
                lastUpdated: new Date().toISOString()
            };
            await this.context.globalState.update('mcpConfiguration', config);
        }
        catch (error) {
            this.log('error', 'Failed to save configuration:', error);
        }
    }
    log(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [MCP] ${message}`;
        if (this.debugMode || level !== 'debug') {
            console[level](logMessage, ...args);
        }
        // Add to activity log if it's an important event
        if (level === 'error' || level === 'warn') {
            this.activityLogs.push({
                id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                timestamp: new Date(),
                serverId: 'system',
                serverName: 'MCP Client',
                action: level === 'error' ? 'server_disconnect' : 'server_connect',
                details: { message, args },
                success: level !== 'error',
                error: level === 'error' ? message : undefined
            });
        }
    }
    async connectToServer(serverConfig) {
        const startTime = Date.now();
        try {
            this.log('info', `🔗 Connecting to MCP server: ${serverConfig.name} (${serverConfig.transport})`);
            // Update status to connecting
            this.updateServerStatus(serverConfig.id, {
                status: types_1.MCPConnectionStatus.Connecting
            });
            // Create client
            const client = new index_js_1.Client({
                name: 'aizen-ai-extension',
                version: '2.0.0'
            });
            // Create transport based on configuration
            const transport = await this.createTransport(serverConfig);
            // Set connection timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Connection timeout')), this.connectionTimeout);
            });
            // Connect with timeout
            await Promise.race([
                client.connect(transport),
                timeoutPromise
            ]);
            // Store client
            this.clients.set(serverConfig.id, client);
            // Load server capabilities and resources
            await this.loadServerCapabilities(serverConfig.id, client);
            // Update status to connected
            this.updateServerStatus(serverConfig.id, {
                status: types_1.MCPConnectionStatus.Connected,
                lastConnected: new Date(),
                lastError: undefined
            });
            // Log successful connection
            const duration = Date.now() - startTime;
            this.logActivity({
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                action: 'server_connect',
                details: { transport: serverConfig.transport, duration },
                success: true,
                duration
            });
            this.log('info', `✅ Connected to ${serverConfig.name} in ${duration}ms`);
            // Emit connection event
            this.emit('server_connected', {
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                transport: serverConfig.transport,
                duration
            });
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.log('error', `❌ Failed to connect to ${serverConfig.name}:`, error);
            // Update status to error
            this.updateServerStatus(serverConfig.id, {
                status: types_1.MCPConnectionStatus.Error,
                lastError: errorMessage
            });
            // Log failed connection
            this.logActivity({
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                action: 'server_connect',
                details: { transport: serverConfig.transport, error: errorMessage },
                success: false,
                error: errorMessage,
                duration
            });
            // Emit error event
            this.emit('server_error', {
                serverId: serverConfig.id,
                serverName: serverConfig.name,
                error: errorMessage
            });
            throw new types_1.MCPConnectionError(`Failed to connect to ${serverConfig.name}: ${errorMessage}`, serverConfig.id, { transport: serverConfig.transport, originalError: error });
        }
    }
    async createTransport(serverConfig) {
        switch (serverConfig.transport) {
            case 'stdio':
                if (!serverConfig.command) {
                    throw new Error('Command is required for stdio transport');
                }
                return new stdio_js_1.StdioClientTransport({
                    command: serverConfig.command,
                    args: serverConfig.args || [],
                    env: { ...process.env, ...serverConfig.env }
                });
            case 'http':
            case 'sse':
                if (!serverConfig.url) {
                    throw new Error('URL is required for HTTP/SSE transport');
                }
                return new sse_js_1.SSEClientTransport(new URL(serverConfig.url));
            case 'streamable-http':
                if (!serverConfig.url) {
                    throw new Error('URL is required for Streamable HTTP transport');
                }
                return new streamableHttp_js_1.StreamableHTTPClientTransport(new URL(serverConfig.url));
            default:
                throw new Error(`Unsupported transport type: ${serverConfig.transport}`);
        }
    }
    async loadServerCapabilities(serverId, client) {
        try {
            // Load tools
            const toolsResponse = await client.listTools();
            for (const tool of toolsResponse.tools) {
                if (!tool.name)
                    continue; // Skip tools without names
                const extendedTool = {
                    ...tool,
                    name: tool.name, // Ensure name is defined
                    serverId,
                    serverName: this.servers.get(serverId)?.name || 'Unknown',
                    riskLevel: this.assessToolRisk(tool),
                    requiresConfirmation: this.securityPolicy.requireConfirmation
                };
                this.tools.set(`${serverId}:${tool.name}`, extendedTool);
            }
            // Load resources
            try {
                const resourcesResponse = await client.listResources();
                for (const resource of resourcesResponse.resources) {
                    if (!resource.uri)
                        continue; // Skip resources without URIs
                    const extendedResource = {
                        ...resource,
                        uri: resource.uri, // Ensure uri is defined
                        serverId,
                        serverName: this.servers.get(serverId)?.name || 'Unknown'
                    };
                    this.resources.set(`${serverId}:${resource.uri}`, extendedResource);
                }
            }
            catch (error) {
                // Resources might not be supported
                this.log('debug', `Server ${serverId} does not support resources:`, error);
            }
            // Load prompts
            try {
                const promptsResponse = await client.listPrompts();
                for (const prompt of promptsResponse.prompts) {
                    if (!prompt.name)
                        continue; // Skip prompts without names
                    const extendedPrompt = {
                        name: prompt.name, // Ensure name is defined
                        description: prompt.description,
                        arguments: prompt.arguments?.map(arg => ({
                            name: arg.name || '',
                            description: arg.description,
                            required: arg.required
                        })),
                        serverId,
                        serverName: this.servers.get(serverId)?.name
                    };
                    this.prompts.set(`${serverId}:${prompt.name}`, extendedPrompt);
                }
            }
            catch (error) {
                // Prompts might not be supported
                this.log('debug', `Server ${serverId} does not support prompts:`, error);
            }
            // Update server status with counts
            const status = this.serverStatus.get(serverId);
            if (status) {
                this.updateServerStatus(serverId, {
                    toolCount: toolsResponse.tools.length,
                    resourceCount: Array.from(this.resources.keys()).filter(k => k.startsWith(`${serverId}:`)).length,
                    promptCount: Array.from(this.prompts.keys()).filter(k => k.startsWith(`${serverId}:`)).length
                });
            }
            this.log('info', `📋 Loaded capabilities for ${serverId}: ${toolsResponse.tools.length} tools, ${Array.from(this.resources.keys()).filter(k => k.startsWith(`${serverId}:`)).length} resources, ${Array.from(this.prompts.keys()).filter(k => k.startsWith(`${serverId}:`)).length} prompts`);
        }
        catch (error) {
            this.log('error', `Failed to load capabilities for ${serverId}:`, error);
            throw error;
        }
    }
    assessToolRisk(tool) {
        if (!this.securityPolicy.riskAssessment) {
            return 'low';
        }
        const toolName = tool.name.toLowerCase();
        const description = (tool.description || '').toLowerCase();
        // High risk indicators
        const highRiskKeywords = ['delete', 'remove', 'destroy', 'execute', 'run', 'shell', 'command', 'admin', 'root', 'sudo'];
        if (highRiskKeywords.some(keyword => toolName.includes(keyword) || description.includes(keyword))) {
            return 'high';
        }
        // Medium risk indicators
        const mediumRiskKeywords = ['write', 'create', 'modify', 'update', 'change', 'edit', 'upload', 'download'];
        if (mediumRiskKeywords.some(keyword => toolName.includes(keyword) || description.includes(keyword))) {
            return 'medium';
        }
        return 'low';
    }
    updateServerStatus(serverId, updates) {
        const currentStatus = this.serverStatus.get(serverId);
        if (currentStatus) {
            const newStatus = { ...currentStatus, ...updates };
            this.serverStatus.set(serverId, newStatus);
            // Emit status change event
            this.emit('server_status_changed', {
                serverId,
                status: newStatus,
                changes: updates
            });
        }
    }
    logActivity(activity) {
        const log = {
            id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            ...activity
        };
        this.activityLogs.push(log);
        // Keep only last 1000 logs to prevent memory issues
        if (this.activityLogs.length > 1000) {
            this.activityLogs = this.activityLogs.slice(-1000);
        }
        // Emit activity event
        this.emit('activity_logged', log);
    }
    // Public API Methods
    async callTool(toolName, args, serverId) {
        const startTime = Date.now();
        try {
            // Find the tool
            const toolKey = serverId ? `${serverId}:${toolName}` :
                Array.from(this.tools.keys()).find(key => key.endsWith(`:${toolName}`));
            if (!toolKey) {
                throw new types_1.MCPToolError(`Tool '${toolName}' not found`, toolName, serverId || 'unknown');
            }
            const tool = this.tools.get(toolKey);
            const client = this.clients.get(tool.serverId);
            if (!client) {
                throw new types_1.MCPConnectionError(`Server '${tool.serverId}' not connected`, tool.serverId);
            }
            // Check user consent if required
            if (this.securityPolicy.userConsentRequired && tool.requiresConfirmation) {
                const hasConsent = await this.checkUserConsent(tool.serverId, toolName, tool.riskLevel || 'medium');
                if (!hasConsent) {
                    throw new types_1.MCPSecurityError(`User consent required for tool '${toolName}'`, tool.serverId);
                }
            }
            this.log('info', `🔧 Calling tool: ${toolName} on server ${tool.serverId}`);
            // Execute tool with timeout
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Tool execution timeout')), this.securityPolicy.maxExecutionTime);
            });
            const result = await Promise.race([
                client.callTool({ name: toolName, arguments: args }),
                timeoutPromise
            ]);
            const duration = Date.now() - startTime;
            // Update tool usage statistics
            tool.lastUsed = new Date();
            tool.usageCount = (tool.usageCount || 0) + 1;
            tool.averageExecutionTime = tool.averageExecutionTime ?
                (tool.averageExecutionTime + duration) / 2 : duration;
            // Log successful execution
            this.logActivity({
                serverId: tool.serverId,
                serverName: tool.serverName || tool.serverId,
                action: 'tool_call',
                details: { toolName, args, duration },
                success: true,
                duration
            });
            this.log('info', `✅ Tool ${toolName} completed successfully in ${duration}ms`);
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.log('error', `❌ Tool ${toolName} failed:`, error);
            // Log failed execution
            this.logActivity({
                serverId: serverId || 'unknown',
                serverName: this.servers.get(serverId || '')?.name || 'Unknown',
                action: 'tool_call',
                details: { toolName, args, error: errorMessage },
                success: false,
                error: errorMessage,
                duration
            });
            throw error;
        }
    }
    async readResource(uri, serverId) {
        try {
            const resourceKey = serverId ? `${serverId}:${uri}` :
                Array.from(this.resources.keys()).find(key => key.endsWith(`:${uri}`));
            if (!resourceKey) {
                throw new types_1.MCPError(`Resource '${uri}' not found`, 'RESOURCE_NOT_FOUND', serverId);
            }
            const resource = this.resources.get(resourceKey);
            const client = this.clients.get(resource.serverId);
            if (!client) {
                throw new types_1.MCPConnectionError(`Server '${resource.serverId}' not connected`, resource.serverId);
            }
            this.log('info', `📖 Reading resource: ${uri} from server ${resource.serverId}`);
            const result = await client.readResource({ uri });
            // Update resource access statistics
            resource.accessCount = (resource.accessCount || 0) + 1;
            // Log successful access
            this.logActivity({
                serverId: resource.serverId,
                serverName: resource.serverName || resource.serverId,
                action: 'resource_read',
                details: { uri },
                success: true
            });
            return result;
        }
        catch (error) {
            this.log('error', `❌ Failed to read resource ${uri}:`, error);
            throw error;
        }
    }
    async getPrompt(promptName, args, serverId) {
        try {
            const promptKey = serverId ? `${serverId}:${promptName}` :
                Array.from(this.prompts.keys()).find(key => key.endsWith(`:${promptName}`));
            if (!promptKey) {
                throw new types_1.MCPError(`Prompt '${promptName}' not found`, 'PROMPT_NOT_FOUND', serverId);
            }
            const prompt = this.prompts.get(promptKey);
            const client = this.clients.get(prompt.serverId);
            if (!client) {
                throw new types_1.MCPConnectionError(`Server '${prompt.serverId}' not connected`, prompt.serverId);
            }
            this.log('info', `📝 Getting prompt: ${promptName} from server ${prompt.serverId}`);
            const result = await client.getPrompt({ name: promptName, arguments: args });
            // Update prompt usage statistics
            prompt.lastUsed = new Date();
            prompt.usageCount = (prompt.usageCount || 0) + 1;
            // Log successful access
            this.logActivity({
                serverId: prompt.serverId,
                serverName: prompt.serverName || prompt.serverId,
                action: 'prompt_get',
                details: { promptName, args },
                success: true
            });
            return result;
        }
        catch (error) {
            this.log('error', `❌ Failed to get prompt ${promptName}:`, error);
            throw error;
        }
    }
    async checkUserConsent(serverId, toolName, riskLevel) {
        const consentKey = `${serverId}:${toolName}`;
        const existingConsent = this.userConsents.get(consentKey);
        // Check if we have valid consent
        if (existingConsent && existingConsent.granted) {
            if (!existingConsent.expiresAt || existingConsent.expiresAt > new Date()) {
                return true;
            }
        }
        // Request user consent
        const serverName = this.servers.get(serverId)?.name || serverId;
        const message = `The tool "${toolName}" from server "${serverName}" wants to execute.\n\nRisk Level: ${riskLevel.toUpperCase()}\n\nDo you want to allow this?`;
        const choice = await vscode.window.showWarningMessage(message, { modal: true }, 'Allow Once', 'Allow Always', 'Deny');
        const granted = choice === 'Allow Once' || choice === 'Allow Always';
        const expiresAt = choice === 'Allow Always' ? undefined : new Date(Date.now() + 3600000); // 1 hour
        // Store consent decision
        const consent = {
            toolName,
            serverId,
            granted,
            timestamp: new Date(),
            expiresAt,
            riskLevel
        };
        this.userConsents.set(consentKey, consent);
        await this.saveConfiguration();
        return granted;
    }
    // Server Management Methods
    async addExternalServer(config) {
        const serverId = `external-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const serverConfig = {
            ...config,
            id: serverId,
            isBuiltIn: false,
            apiKeyConfigured: !config.requiresApiKey
        };
        this.servers.set(serverId, serverConfig);
        this.serverStatus.set(serverId, {
            id: serverId,
            name: serverConfig.name,
            status: types_1.MCPConnectionStatus.Disconnected,
            toolCount: 0,
            resourceCount: 0,
            promptCount: 0,
            isBuiltIn: false
        });
        await this.saveConfiguration();
        // Auto-connect if enabled
        if (serverConfig.enabled && serverConfig.autoStart) {
            try {
                await this.connectToServer(serverConfig);
            }
            catch (error) {
                this.log('warn', `Failed to auto-connect to new server ${serverConfig.name}:`, error);
            }
        }
        this.emit('server_added', { serverId, serverConfig });
        return serverId;
    }
    async removeServer(serverId) {
        const serverConfig = this.servers.get(serverId);
        if (!serverConfig) {
            throw new types_1.MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
        }
        if (serverConfig.isBuiltIn) {
            throw new types_1.MCPSecurityError(`Cannot remove built-in server '${serverId}'`, serverId);
        }
        // Disconnect if connected
        await this.disconnectFromServer(serverId);
        // Remove from maps
        this.servers.delete(serverId);
        this.serverStatus.delete(serverId);
        // Remove associated tools, resources, and prompts
        for (const [key, tool] of this.tools.entries()) {
            if (tool.serverId === serverId) {
                this.tools.delete(key);
            }
        }
        for (const [key, resource] of this.resources.entries()) {
            if (resource.serverId === serverId) {
                this.resources.delete(key);
            }
        }
        for (const [key, prompt] of this.prompts.entries()) {
            if (prompt.serverId === serverId) {
                this.prompts.delete(key);
            }
        }
        await this.saveConfiguration();
        this.emit('server_removed', { serverId, serverConfig });
    }
    async disconnectFromServer(serverId) {
        const client = this.clients.get(serverId);
        if (!client) {
            return; // Already disconnected
        }
        try {
            this.log('info', `🔌 Disconnecting from server: ${serverId}`);
            await client.close();
            this.clients.delete(serverId);
            this.updateServerStatus(serverId, {
                status: types_1.MCPConnectionStatus.Disconnected
            });
            this.logActivity({
                serverId,
                serverName: this.servers.get(serverId)?.name || serverId,
                action: 'server_disconnect',
                details: {},
                success: true
            });
            this.emit('server_disconnected', { serverId });
        }
        catch (error) {
            this.log('error', `Failed to disconnect from server ${serverId}:`, error);
            throw error;
        }
    }
    async reconnectToServer(serverId) {
        const serverConfig = this.servers.get(serverId);
        if (!serverConfig) {
            throw new types_1.MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
        }
        // Disconnect first if connected
        await this.disconnectFromServer(serverId);
        // Reconnect
        await this.connectToServer(serverConfig);
    }
    // Getter Methods
    getAvailableTools() {
        return Array.from(this.tools.values());
    }
    getAvailableResources() {
        return Array.from(this.resources.values());
    }
    getAvailablePrompts() {
        return Array.from(this.prompts.values());
    }
    getServerStatus(serverId) {
        if (serverId) {
            const status = this.serverStatus.get(serverId);
            if (!status) {
                throw new types_1.MCPError(`Server '${serverId}' not found`, 'SERVER_NOT_FOUND', serverId);
            }
            return status;
        }
        return Array.from(this.serverStatus.values());
    }
    getConnectedServers() {
        return Array.from(this.servers.values()).filter(server => this.clients.has(server.id));
    }
    getActivityLogs(limit) {
        const logs = [...this.activityLogs].reverse(); // Most recent first
        return limit ? logs.slice(0, limit) : logs;
    }
    getUserConsents() {
        return Array.from(this.userConsents.values());
    }
    getSecurityPolicy() {
        return { ...this.securityPolicy };
    }
    async updateSecurityPolicy(policy) {
        this.securityPolicy = { ...this.securityPolicy, ...policy };
        await this.saveConfiguration();
        this.emit('security_policy_updated', this.securityPolicy);
    }
    // Status and Health Methods
    async testServerConnection(serverId) {
        try {
            const client = this.clients.get(serverId);
            if (!client) {
                return false;
            }
            // Try to ping the server or list tools as a health check
            await client.listTools();
            return true;
        }
        catch (error) {
            this.log('warn', `Health check failed for server ${serverId}:`, error);
            return false;
        }
    }
    getHealthStatus() {
        const errors = [];
        const totalServers = this.servers.size;
        const connectedServers = this.clients.size;
        // Check for servers in error state
        for (const [serverId, status] of this.serverStatus.entries()) {
            if (status.status === types_1.MCPConnectionStatus.Error && status.lastError) {
                errors.push(`${status.name}: ${status.lastError}`);
            }
        }
        return {
            isHealthy: errors.length === 0 && connectedServers > 0,
            connectedServers,
            totalServers,
            totalTools: this.tools.size,
            totalResources: this.resources.size,
            totalPrompts: this.prompts.size,
            errors
        };
    }
    // Cleanup and Disposal
    async dispose() {
        this.log('info', '🔄 Disposing MCP client...');
        // Disconnect from all servers
        const disconnectPromises = Array.from(this.clients.keys()).map(serverId => this.disconnectFromServer(serverId).catch(error => this.log('warn', `Error disconnecting from ${serverId}:`, error)));
        await Promise.all(disconnectPromises);
        // Clear all data
        this.clients.clear();
        this.tools.clear();
        this.resources.clear();
        this.prompts.clear();
        this.activityLogs.length = 0;
        // Save final configuration
        await this.saveConfiguration();
        this.isInitialized = false;
        this.log('info', '✅ MCP client disposed successfully');
        this.emit('disposed');
    }
}
exports.AizenMCPClient = AizenMCPClient;
//# sourceMappingURL=MCPClient.js.map