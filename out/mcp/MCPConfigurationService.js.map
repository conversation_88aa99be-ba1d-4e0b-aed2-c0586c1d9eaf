{"version": 3, "file": "MCPConfigurationService.js", "sourceRoot": "", "sources": ["../../src/mcp/MCPConfigurationService.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,uEAA0G;AAG1G,iEAA8D;AAG9D,MAAa,uBAAuB;IAOhC,YACI,OAAgC,EAChC,SAAyB,EACzB,eAAmC;QAL/B,kBAAa,GAAG,KAAK,CAAC;QAO1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,IAAI,iDAAuB,CAAC,OAAO,CAAC,CAAC;QAE1D,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,kBAAkB;QACtB,mCAAmC;QACnC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAChD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YACjD,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAE5D,6BAA6B;YAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAE5D,iCAAiC;YACjC,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAE/C,2CAA2C;YAC3C,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAEvC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QAExE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;QACvF,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,MAAsB;QAC5D,IAAI,CAAC,MAAM,CAAC,QAAQ;YAAE,OAAO;QAE7B,MAAM,cAAc,GAA+B,EAAE,CAAC;QAEtD,IAAI,MAAM,CAAC,QAAQ,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YACpD,cAAc,CAAC,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QAC7E,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACjD,cAAc,CAAC,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QACvE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YACjC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QACnE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YACjC,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC;QACnE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YAClC,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;QAClE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YAClC,cAAc,CAAC,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;IAC1E,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAsB;QACpD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAElF,KAAK,MAAM,YAAY,IAAI,eAAe,EAAE,CAAC;YACzC,IAAI,CAAC;gBACD,gCAAgC;gBAChC,MAAM,IAAI,CAAC,2BAA2B,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE7D,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAErD,OAAO,CAAC,GAAG,CAAC,6BAA6B,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YAElE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,YAAY,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACtE,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,8BAA8B,YAAY,CAAC,IAAI,MAAM,KAAK,EAAE,CAC/D,CAAC;YACN,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,2BAA2B,CACrC,YAAqC,EACrC,YAA4B;QAE5B,yCAAyC;QACzC,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,cAAc,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,mCAAmC,YAAY,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1E,CAAC;QAED,2CAA2C;QAC3C,MAAM,gBAAgB,GAAG,2CAAoB,CAAC,2BAA2B,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAExG,2BAA2B;QAC3B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,IAAI,KAAK,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,+BAA+B;QAC/B,IAAI,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,cAAc,GAAG,iCAAiC,YAAY,CAAC,IAAI,OAAO,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACvH,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE7B,kDAAkD;YAClD,IAAI,gBAAgB,CAAC,SAAS,KAAK,MAAM,EAAE,CAAC;gBACxC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,kCAAkC,YAAY,CAAC,IAAI,kCAAkC,EACrF,cAAc,CACjB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACZ,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;wBAC5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;oBACzD,CAAC;gBACL,CAAC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,8CAA8C;QAC9C,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;YAChE,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC;YAE7C,IAAI,MAAM,EAAE,CAAC;gBACT,MAAM,IAAI,KAAK,CAAC,yCAAyC,MAAM,aAAa,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,4CAA4C,YAAY,CAAC,IAAI,WAAW,gBAAgB,CAAC,SAAS,GAAG,CAAC,CAAC;IACvH,CAAC;IAIO,KAAK,CAAC,yBAAyB,CAAC,MAAsB;QAC1D,IAAI,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;YAElE,kCAAkC;YAClC,MAAM,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAE/C,+BAA+B;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACtF,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;YAEnF,qDAAqD;YACrD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;gBACzC,MAAM,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,KAAK,aAAa,CAAC,EAAE,CAAC,CAAC;gBAC1F,IAAI,CAAC,WAAW,EAAE,CAAC;oBACf,IAAI,CAAC;wBACD,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;wBACpD,OAAO,CAAC,GAAG,CAAC,uBAAuB,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC7D,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,aAAa,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC3E,CAAC;gBACL,CAAC;YACL,CAAC;YAED,wBAAwB;YACxB,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;gBAC7C,IAAI,CAAC;oBACD,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,CAAC,CAAC;oBAE7E,IAAI,cAAc,EAAE,CAAC;wBACjB,4DAA4D;wBAC5D,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;wBACrD,MAAM,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;wBAChE,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;wBACxD,OAAO,CAAC,GAAG,CAAC,sBAAsB,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC9D,CAAC;yBAAM,CAAC;wBACJ,iBAAiB;wBACjB,MAAM,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;wBAChE,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;wBACxD,OAAO,CAAC,GAAG,CAAC,mBAAmB,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC3D,CAAC;gBAEL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,eAAe,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;oBAC5E,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5B,mCAAmC,eAAe,CAAC,IAAI,MAAM,KAAK,EAAE,CACvE,CAAC;gBACN,CAAC;YACL,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;QAEpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;QAClF,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB;QACvB,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,0BAA0B;QAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QAC5D,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,4BAA4B;QAC9B,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YAC5D,MAAM,MAAM,GAAa,EAAE,CAAC;YAE5B,uBAAuB;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;YAChF,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACvC,IAAI,CAAC;oBACD,MAAM,IAAI,CAAC,2BAA2B,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBACjE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,MAAM,CAAC,IAAI,CAAC,WAAW,YAAY,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC;gBAC3D,CAAC;YACL,CAAC;YAED,OAAO;gBACH,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;gBAC5B,MAAM;aACT,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,6BAA6B,KAAK,EAAE,CAAC;aACjD,CAAC;QACN,CAAC;IACL,CAAC;IAED,oBAAoB;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,EAAE,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,mBAAmB;QACrB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;QAC5D,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QACxC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAmB,CAAC;YACxD,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yCAAyC,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AAxRD,0DAwRC"}