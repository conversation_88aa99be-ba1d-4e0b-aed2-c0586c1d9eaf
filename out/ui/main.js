/**
 * Aizen AI Extension - Modern UI with Liquid Glass Design
 * Main UI Controller - Handles all user interactions and state management
 */
class <PERSON>zenUIController {
    constructor() {
        this.state = {
            currentTab: 'actions',
            currentMode: 'auto',
            currentAgentMode: 'ask',
            autoMode: false
        };
        this.elements = {};
        this.initializeElements();
        this.setupEventListeners();
        this.setupMessageListener();
        this.initializeUI();
        this.initializeLucideIcons();
    }
    initializeElements() {
        // Tab elements
        this.elements.tabButtons = document.querySelectorAll('.tab-btn');
        // Mode selector elements
        this.elements.modeButton = document.getElementById('modeButton');
        this.elements.modeDropdown = document.getElementById('modeDropdown');
        this.elements.modeOptions = document.querySelectorAll('.mode-option');
        // Agent mode selector elements
        this.elements.agentModeButton = document.getElementById('agentModeButton');
        this.elements.agentModeDropdown = document.getElementById('agentModeDropdown');
        this.elements.agentModeOptions = document.querySelectorAll('.agent-mode-option');
        // Input elements
        this.elements.chatInput = document.getElementById('chatInput');
        this.elements.sendButton = document.getElementById('sendButton');
        this.elements.autoToggle = document.getElementById('autoToggle');
        // Chat elements
        this.elements.chatMessages = document.getElementById('chatMessages');
    }
    setupEventListeners() {
        // Tab switching
        this.elements.tabButtons?.forEach((btn) => {
            btn.addEventListener('click', (e) => {
                const target = e.target;
                const tab = target.closest('.tab-btn')?.getAttribute('data-tab');
                if (tab)
                    this.switchTab(tab);
            });
        });
        // Mode selector
        this.elements.modeButton?.addEventListener('click', () => {
            this.toggleDropdown('mode');
        });
        this.elements.modeOptions?.forEach((option) => {
            option.addEventListener('click', (e) => {
                const target = e.target;
                const mode = target.closest('.mode-option')?.getAttribute('data-mode');
                if (mode)
                    this.selectMode(mode);
            });
        });
        // Agent mode selector
        this.elements.agentModeButton?.addEventListener('click', () => {
            this.toggleDropdown('agentMode');
        });
        this.elements.agentModeOptions?.forEach((option) => {
            option.addEventListener('click', (e) => {
                const target = e.target;
                const mode = target.closest('.agent-mode-option')?.getAttribute('data-mode');
                if (mode)
                    this.selectAgentMode(mode);
            });
        });
        // Chat input
        this.elements.chatInput?.addEventListener('keypress', (e) => {
            if (e instanceof KeyboardEvent && e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        this.elements.sendButton?.addEventListener('click', () => {
            this.sendMessage();
        });
        // Auto toggle
        this.elements.autoToggle?.addEventListener('change', (e) => {
            const target = e.target;
            this.state.autoMode = target.checked;
            this.updateAutoMode();
        });
        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            const target = e.target;
            if (!target.closest('.mode-selector')) {
                this.closeDropdown('mode');
            }
            if (!target.closest('.agent-mode-selector')) {
                this.closeDropdown('agentMode');
            }
        });
        // Navigation buttons
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const target = e.target;
                const button = target.closest('.nav-btn');
                const action = button.getAttribute('data-action');
                if (action)
                    this.handleNavAction(action);
            });
        });
    }
    setupMessageListener() {
        window.addEventListener('message', (event) => {
            const message = event.data;
            switch (message.command) {
                case 'chatMessage':
                    this.addChatMessage(message.data);
                    break;
                case 'themeChanged':
                    this.handleThemeChange(message.theme);
                    break;
                case 'updateState':
                    this.updateUIState(message.data);
                    break;
            }
        });
    }
    initializeUI() {
        // Apply initial theme
        this.applyTheme();
        // Set initial states
        this.updateModeDisplay();
        this.updateAgentModeDisplay();
        // Add welcome animation
        setTimeout(() => {
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.classList.add('animate-in');
            }
        }, 300);
    }
    switchTab(tab) {
        this.state.currentTab = tab;
        // Update tab buttons
        this.elements.tabButtons?.forEach((btn) => {
            const htmlBtn = btn;
            htmlBtn.classList.toggle('active', htmlBtn.getAttribute('data-tab') === tab);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'tabChanged',
            data: { tab }
        });
        // Add visual feedback
        this.addRippleEffect(event?.target);
    }
    toggleDropdown(type) {
        const dropdown = type === 'mode' ? this.elements.modeDropdown : this.elements.agentModeDropdown;
        dropdown?.classList.toggle('show');
    }
    closeDropdown(type) {
        const dropdown = type === 'mode' ? this.elements.modeDropdown : this.elements.agentModeDropdown;
        dropdown?.classList.remove('show');
    }
    selectMode(mode) {
        this.state.currentMode = mode;
        this.updateModeDisplay();
        this.closeDropdown('mode');
        // Update active option
        this.elements.modeOptions?.forEach((option) => {
            const htmlOption = option;
            htmlOption.classList.toggle('active', htmlOption.getAttribute('data-mode') === mode);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'modeChanged',
            data: { mode }
        });
    }
    selectAgentMode(mode) {
        this.state.currentAgentMode = mode;
        this.updateAgentModeDisplay();
        this.closeDropdown('agentMode');
        // Update active option
        this.elements.agentModeOptions?.forEach((option) => {
            const htmlOption = option;
            htmlOption.classList.toggle('active', htmlOption.getAttribute('data-mode') === mode);
        });
        // Send message to extension
        vscode.postMessage({
            command: 'agentModeChanged',
            data: { mode }
        });
    }
    updateModeDisplay() {
        const modeText = this.elements.modeButton?.querySelector('.mode-text');
        if (modeText) {
            modeText.textContent = this.state.currentMode.charAt(0).toUpperCase() + this.state.currentMode.slice(1);
        }
    }
    updateAgentModeDisplay() {
        const agentModeText = this.elements.agentModeButton?.querySelector('.agent-mode-text');
        if (agentModeText) {
            agentModeText.textContent = this.state.currentAgentMode.charAt(0).toUpperCase() + this.state.currentAgentMode.slice(1);
        }
    }
    updateAutoMode() {
        // Send message to extension
        vscode.postMessage({
            command: 'autoModeChanged',
            data: { autoMode: this.state.autoMode }
        });
    }
    sendMessage() {
        const input = this.elements.chatInput;
        const message = input?.value.trim();
        if (!message)
            return;
        // Clear input
        input.value = '';
        // Send message to extension
        vscode.postMessage({
            command: 'sendMessage',
            data: {
                message,
                mode: this.state.currentMode,
                agentMode: this.state.currentAgentMode,
                autoMode: this.state.autoMode
            }
        });
        // Add user message to chat immediately
        this.addChatMessage({
            type: 'user',
            message,
            timestamp: new Date().toISOString()
        });
    }
    addChatMessage(data) {
        const messagesContainer = this.elements.chatMessages;
        if (!messagesContainer)
            return;
        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${data.type} glass`;
        const time = new Date(data.timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${data.type === 'user' ? 'You' : 'Aizen AI'}</span>
                <span class="message-time">${time}</span>
            </div>
            <div class="message-content">${this.formatMessage(data.message)}</div>
        `;
        messagesContainer.appendChild(messageElement);
        // Scroll to bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
        // Add animation
        setTimeout(() => {
            messageElement.classList.add('animate-in');
        }, 10);
    }
    formatMessage(message) {
        // Basic markdown-like formatting
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    handleNavAction(action) {
        // Handle specific navigation actions
        switch (action) {
            case 'new-chat':
                this.createNewChat();
                break;
            case 'history':
                this.showQueryHistory();
                break;
            case 'mcp-store':
                this.openMCPStore();
                break;
            case 'advanced-mode':
                this.toggleAdvancedMode();
                break;
            default:
                // Send generic action to extension
                console.log('🔧 Sending navAction message:', action);
                console.log('🔧 vscode API available:', typeof vscode !== 'undefined');
                if (typeof vscode !== 'undefined') {
                    vscode.postMessage({
                        command: 'navAction',
                        data: { action }
                    });
                    console.log('✅ navAction message sent');
                } else {
                    console.error('❌ vscode API not available');
                }
        }
    }
    createNewChat() {
        // Clear current chat
        const messagesContainer = this.elements.chatMessages;
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="welcome-message glass">
                    <div class="welcome-icon">
                        <i class="hgi-stroke hgi-artificial-intelligence-02"></i>
                    </div>
                    <h2>Welcome to Aizen AI</h2>
                    <p>Your revolutionary AI assistant with advanced reasoning and swarm intelligence</p>
                    <div class="welcome-features">
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-brain"></i>
                            <span>Advanced Reasoning</span>
                        </div>
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-hierarchy-square-02"></i>
                            <span>Swarm Intelligence</span>
                        </div>
                        <div class="feature-item">
                            <i class="hgi-stroke hgi-rocket-01"></i>
                            <span>Self-Improvement</span>
                        </div>
                    </div>
                </div>
            `;
        }
        // Clear input
        const input = this.elements.chatInput;
        if (input)
            input.value = '';
        // Send message to extension
        vscode.postMessage({
            command: 'newChat',
            data: {}
        });
        // Show notification
        this.showNotification('New chat started', 'success');
    }
    showQueryHistory() {
        vscode.postMessage({
            command: 'showHistory',
            data: {}
        });
        this.showNotification('Opening query history...', 'info');
    }
    openMCPStore() {
        vscode.postMessage({
            command: 'openMCPStore',
            data: {}
        });
        this.showNotification('Opening MCP Store...', 'info');
    }
    toggleAdvancedMode() {
        vscode.postMessage({
            command: 'toggleAdvancedMode',
            data: {}
        });
        this.showNotification('Advanced mode toggled', 'success');
    }
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type} glass`;
        notification.innerHTML = `
            <i class="hgi-stroke ${type === 'success' ? 'hgi-check-circle' : type === 'error' ? 'hgi-alert-circle' : 'hgi-information-circle'}"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(notification);
        // Animate in
        setTimeout(() => notification.classList.add('show'), 10);
        // Remove after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    handleThemeChange(theme) {
        document.documentElement.className = theme;
        this.applyTheme();
    }
    applyTheme() {
        // Apply theme-specific styles
        const isDark = document.documentElement.classList.contains('vscode-dark');
        const isHighContrast = document.documentElement.classList.contains('vscode-high-contrast');
        // Update CSS custom properties based on theme
        const root = document.documentElement;
        if (isHighContrast) {
            root.style.setProperty('--glass-primary', 'rgba(255, 255, 255, 0.1)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.3)');
        }
        else if (isDark) {
            root.style.setProperty('--glass-primary', 'rgba(255, 255, 255, 0.08)');
            root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.12)');
        }
        else {
            root.style.setProperty('--glass-primary', 'rgba(0, 0, 0, 0.05)');
            root.style.setProperty('--glass-border', 'rgba(0, 0, 0, 0.1)');
        }
    }
    updateUIState(data) {
        // Update UI state from extension
        Object.assign(this.state, data);
        this.updateModeDisplay();
        this.updateAgentModeDisplay();
    }
    initializeLucideIcons() {
        // Initialize Lucide icons when available
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        } else {
            // Retry after a short delay if Lucide isn't loaded yet
            setTimeout(() => this.initializeLucideIcons(), 100);
        }
    }
    addRippleEffect(element) {
        if (!element)
            return;
        const ripple = document.createElement('span');
        ripple.className = 'ripple';
        ripple.style.cssText = `
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        setTimeout(() => ripple.remove(), 600);
    }
}
// Initialize the UI when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new AizenUIController());
}
else {
    new AizenUIController();
}
//# sourceMappingURL=main.js.map