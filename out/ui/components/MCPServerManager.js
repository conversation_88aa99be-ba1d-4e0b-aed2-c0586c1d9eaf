"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPServerManager = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
/**
 * MCP Server Manager Component
 *
 * Provides a comprehensive UI for managing MCP servers including:
 * - Built-in server status and configuration
 * - External server addition and management
 * - Real-time connection status
 * - Tool and resource counts
 * - Security settings
 */
const react_1 = require("react");
const types_1 = require("../../mcp/types");
const MCPServerManager = ({ vscode, onServerAdded, onServerRemoved, onServerToggled }) => {
    const [servers, setServers] = (0, react_1.useState)(new Map());
    const [loading, setLoading] = (0, react_1.useState)(true);
    const [showAddDialog, setShowAddDialog] = (0, react_1.useState)(false);
    const [selectedServer, setSelectedServer] = (0, react_1.useState)(null);
    (0, react_1.useEffect)(() => {
        loadServers();
        // Listen for server updates
        const handleMessage = (event) => {
            const message = event.data;
            switch (message.type) {
                case 'serverStatusUpdate':
                    updateServerStatus(message.serverId, message.status);
                    break;
                case 'serverAdded':
                    loadServers(); // Reload all servers
                    break;
                case 'serverRemoved':
                    removeServer(message.serverId);
                    break;
            }
        };
        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, []);
    const loadServers = async () => {
        try {
            setLoading(true);
            vscode.postMessage({ type: 'getMCPServers' });
        }
        catch (error) {
            console.error('Failed to load MCP servers:', error);
        }
        finally {
            setLoading(false);
        }
    };
    const updateServerStatus = (serverId, status) => {
        setServers(prev => {
            const newServers = new Map(prev);
            const serverData = newServers.get(serverId);
            if (serverData) {
                newServers.set(serverId, {
                    ...serverData,
                    status
                });
            }
            return newServers;
        });
    };
    const removeServer = (serverId) => {
        setServers(prev => {
            const newServers = new Map(prev);
            newServers.delete(serverId);
            return newServers;
        });
    };
    const handleToggleServer = async (serverId, enabled) => {
        try {
            vscode.postMessage({
                type: 'toggleMCPServer',
                serverId,
                enabled
            });
            onServerToggled?.(serverId, enabled);
        }
        catch (error) {
            console.error('Failed to toggle server:', error);
        }
    };
    const handleTestConnection = async (serverId) => {
        try {
            vscode.postMessage({
                type: 'testMCPConnection',
                serverId
            });
        }
        catch (error) {
            console.error('Failed to test connection:', error);
        }
    };
    const handleRemoveServer = async (serverId) => {
        const serverData = servers.get(serverId);
        if (!serverData)
            return;
        const confirmed = await new Promise((resolve) => {
            vscode.postMessage({
                type: 'confirmDialog',
                message: `Are you sure you want to remove the server "${serverData.config.name}"?`,
                callback: resolve
            });
        });
        if (confirmed) {
            try {
                vscode.postMessage({
                    type: 'removeMCPServer',
                    serverId
                });
                onServerRemoved?.(serverId);
            }
            catch (error) {
                console.error('Failed to remove server:', error);
            }
        }
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case types_1.MCPConnectionStatus.Connected:
                return '✅';
            case types_1.MCPConnectionStatus.Connecting:
                return '🔄';
            case types_1.MCPConnectionStatus.Disconnected:
                return '⚫';
            case types_1.MCPConnectionStatus.Error:
                return '❌';
            case types_1.MCPConnectionStatus.Reconnecting:
                return '🔄';
            default:
                return '❓';
        }
    };
    const getStatusColor = (status) => {
        switch (status) {
            case types_1.MCPConnectionStatus.Connected:
                return '#10b981'; // green
            case types_1.MCPConnectionStatus.Connecting:
            case types_1.MCPConnectionStatus.Reconnecting:
                return '#f59e0b'; // yellow
            case types_1.MCPConnectionStatus.Disconnected:
                return '#6b7280'; // gray
            case types_1.MCPConnectionStatus.Error:
                return '#ef4444'; // red
            default:
                return '#6b7280';
        }
    };
    const renderServerCard = (serverData) => {
        const { config, status, tools, resources, prompts } = serverData;
        return ((0, jsx_runtime_1.jsxs)("div", { className: "mcp-server-card glass", children: [(0, jsx_runtime_1.jsxs)("div", { className: "server-header", children: [(0, jsx_runtime_1.jsxs)("div", { className: "server-info", children: [(0, jsx_runtime_1.jsxs)("div", { className: "server-title", children: [(0, jsx_runtime_1.jsx)("span", { className: "server-icon", children: config.icon || '🔧' }), (0, jsx_runtime_1.jsx)("h4", { children: config.name }), config.isBuiltIn && (0, jsx_runtime_1.jsx)("span", { className: "built-in-badge", children: "Built-in" })] }), (0, jsx_runtime_1.jsx)("p", { className: "server-description", children: config.description }), (0, jsx_runtime_1.jsxs)("div", { className: "server-stats", children: [(0, jsx_runtime_1.jsxs)("span", { className: "stat", children: [(0, jsx_runtime_1.jsx)("span", { className: "stat-icon", children: "\uD83D\uDD27" }), status.toolCount, " tools"] }), (0, jsx_runtime_1.jsxs)("span", { className: "stat", children: [(0, jsx_runtime_1.jsx)("span", { className: "stat-icon", children: "\uD83D\uDCC4" }), status.resourceCount, " resources"] }), (0, jsx_runtime_1.jsxs)("span", { className: "stat", children: [(0, jsx_runtime_1.jsx)("span", { className: "stat-icon", children: "\uD83D\uDCAC" }), status.promptCount, " prompts"] })] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "server-status", children: [(0, jsx_runtime_1.jsxs)("span", { className: "status-badge", style: { color: getStatusColor(status.status) }, children: [getStatusIcon(status.status), " ", status.status] }), status.lastError && ((0, jsx_runtime_1.jsxs)("div", { className: "error-message", title: status.lastError, children: ["\u26A0\uFE0F ", status.lastError.substring(0, 50), "..."] }))] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "server-actions", children: [(0, jsx_runtime_1.jsxs)("label", { className: "toggle-switch", children: [(0, jsx_runtime_1.jsx)("input", { type: "checkbox", checked: config.enabled, onChange: (e) => handleToggleServer(config.id, e.target.checked) }), (0, jsx_runtime_1.jsx)("span", { className: "toggle-slider glass" })] }), (0, jsx_runtime_1.jsx)("button", { className: "btn btn-small btn-secondary", onClick: () => handleTestConnection(config.id), disabled: status.status === types_1.MCPConnectionStatus.Connecting, title: "Test connection", children: "\uD83D\uDD0D Test" }), (0, jsx_runtime_1.jsx)("button", { className: "btn btn-small", onClick: () => setSelectedServer(config.id), title: "View details", children: "\uD83D\uDCCB Details" }), !config.isBuiltIn && ((0, jsx_runtime_1.jsx)("button", { className: "btn btn-small btn-danger", onClick: () => handleRemoveServer(config.id), title: "Remove server", children: "\uD83D\uDDD1\uFE0F Remove" }))] }), selectedServer === config.id && ((0, jsx_runtime_1.jsxs)("div", { className: "server-details", children: [(0, jsx_runtime_1.jsxs)("div", { className: "details-header", children: [(0, jsx_runtime_1.jsx)("h5", { children: "Server Details" }), (0, jsx_runtime_1.jsx)("button", { className: "close-btn", onClick: () => setSelectedServer(null), children: "\u2715" })] }), (0, jsx_runtime_1.jsxs)("div", { className: "details-content", children: [(0, jsx_runtime_1.jsxs)("div", { className: "detail-section", children: [(0, jsx_runtime_1.jsx)("h6", { children: "Configuration" }), (0, jsx_runtime_1.jsxs)("div", { className: "detail-grid", children: [(0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Transport:" }), (0, jsx_runtime_1.jsx)("span", { className: "detail-value", children: config.transport })] }), (0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Version:" }), (0, jsx_runtime_1.jsx)("span", { className: "detail-value", children: config.version || 'Unknown' })] }), (0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Timeout:" }), (0, jsx_runtime_1.jsxs)("span", { className: "detail-value", children: [config.timeout, "ms"] })] }), (0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Auto-start:" }), (0, jsx_runtime_1.jsx)("span", { className: "detail-value", children: config.autoStart ? 'Yes' : 'No' })] })] })] }), tools.length > 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "detail-section", children: [(0, jsx_runtime_1.jsxs)("h6", { children: ["Available Tools (", tools.length, ")"] }), (0, jsx_runtime_1.jsxs)("div", { className: "tools-list", children: [tools.slice(0, 5).map(tool => ((0, jsx_runtime_1.jsxs)("div", { className: "tool-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "tool-name", children: tool.name }), (0, jsx_runtime_1.jsx)("span", { className: "tool-description", children: tool.description }), tool.riskLevel && ((0, jsx_runtime_1.jsx)("span", { className: `risk-badge risk-${tool.riskLevel}`, children: tool.riskLevel }))] }, tool.name))), tools.length > 5 && ((0, jsx_runtime_1.jsxs)("div", { className: "more-items", children: ["+", tools.length - 5, " more tools"] }))] })] })), status.lastConnected && ((0, jsx_runtime_1.jsxs)("div", { className: "detail-section", children: [(0, jsx_runtime_1.jsx)("h6", { children: "Connection Info" }), (0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Last connected:" }), (0, jsx_runtime_1.jsx)("span", { className: "detail-value", children: status.lastConnected.toLocaleString() })] }), status.uptime && ((0, jsx_runtime_1.jsxs)("div", { className: "detail-item", children: [(0, jsx_runtime_1.jsx)("span", { className: "detail-label", children: "Uptime:" }), (0, jsx_runtime_1.jsxs)("span", { className: "detail-value", children: [Math.round(status.uptime / 1000), "s"] })] }))] }))] })] }))] }, config.id));
    };
    if (loading) {
        return ((0, jsx_runtime_1.jsxs)("div", { className: "mcp-loading", children: [(0, jsx_runtime_1.jsx)("div", { className: "loading-spinner" }), (0, jsx_runtime_1.jsx)("p", { children: "Loading MCP servers..." })] }));
    }
    const builtInServers = Array.from(servers.values()).filter(s => s.config.isBuiltIn);
    const externalServers = Array.from(servers.values()).filter(s => !s.config.isBuiltIn);
    return ((0, jsx_runtime_1.jsxs)("div", { className: "mcp-server-manager", children: [(0, jsx_runtime_1.jsxs)("div", { className: "manager-header", children: [(0, jsx_runtime_1.jsx)("h3", { children: "MCP Server Configuration" }), (0, jsx_runtime_1.jsxs)("button", { className: "btn btn-primary", onClick: () => setShowAddDialog(true), children: [(0, jsx_runtime_1.jsx)("span", { className: "btn-icon", children: "\u2795" }), "Add External Server"] })] }), builtInServers.length > 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "server-section", children: [(0, jsx_runtime_1.jsx)("h4", { children: "Built-in Servers" }), (0, jsx_runtime_1.jsx)("p", { className: "section-description", children: "These servers are pre-configured with Aizen AI and ready to use." }), (0, jsx_runtime_1.jsx)("div", { className: "servers-grid", children: builtInServers.map(serverData => renderServerCard(serverData)) })] })), externalServers.length > 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "server-section", children: [(0, jsx_runtime_1.jsx)("h4", { children: "External Servers" }), (0, jsx_runtime_1.jsx)("p", { className: "section-description", children: "Custom MCP servers you've added to extend functionality." }), (0, jsx_runtime_1.jsx)("div", { className: "servers-grid", children: externalServers.map(serverData => renderServerCard(serverData)) })] })), externalServers.length === 0 && ((0, jsx_runtime_1.jsxs)("div", { className: "empty-state", children: [(0, jsx_runtime_1.jsx)("div", { className: "empty-icon", children: "\uD83D\uDD27" }), (0, jsx_runtime_1.jsx)("h4", { children: "No External Servers" }), (0, jsx_runtime_1.jsx)("p", { children: "Add external MCP servers to extend Aizen AI's capabilities." }), (0, jsx_runtime_1.jsx)("button", { className: "btn btn-primary", onClick: () => setShowAddDialog(true), children: "Add Your First Server" })] })), showAddDialog && ((0, jsx_runtime_1.jsx)(AddServerDialog, { vscode: vscode, onClose: () => setShowAddDialog(false), onServerAdded: (serverId) => {
                    setShowAddDialog(false);
                    onServerAdded?.(serverId);
                    loadServers();
                } }))] }));
};
exports.MCPServerManager = MCPServerManager;
// Add Server Dialog Component (will be implemented separately)
const AddServerDialog = ({ vscode, onClose, onServerAdded }) => {
    // This will be implemented in the next part
    return ((0, jsx_runtime_1.jsx)("div", { className: "dialog-overlay", children: (0, jsx_runtime_1.jsxs)("div", { className: "dialog glass", children: [(0, jsx_runtime_1.jsx)("h4", { children: "Add External MCP Server" }), (0, jsx_runtime_1.jsx)("p", { children: "Dialog implementation coming next..." }), (0, jsx_runtime_1.jsx)("button", { onClick: onClose, children: "Close" })] }) }));
};
//# sourceMappingURL=MCPServerManager.js.map