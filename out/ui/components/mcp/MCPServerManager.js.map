{"version": 3, "file": "MCPServerManager.js", "sourceRoot": "", "sources": ["../../../../src/ui/components/mcp/MCPServerManager.tsx"], "names": [], "mappings": ";;;;AAAA,kCAAkC;AAClC,iCAAmD;AACnD,8CAO4B;AAWrB,MAAM,gBAAgB,GAAoC,CAAC,EAC9D,WAAW,EACX,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,OAAO,EACV,EAAE,EAAE;IACD,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IAC1E,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC1D,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,IAAA,gBAAQ,EAA2B;QAC7E,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,iBAAiB;QAC5B,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;KAClB,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,CAAC,MAA2B,EAAE,EAAE;QAClD,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,2BAAmB,CAAC,SAAS;gBAC9B,OAAO,IAAI,CAAC;YAChB,KAAK,2BAAmB,CAAC,UAAU;gBAC/B,OAAO,IAAI,CAAC;YAChB,KAAK,2BAAmB,CAAC,KAAK;gBAC1B,OAAO,IAAI,CAAC;YAChB,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,IAAI,CAAC;YAChB;gBACI,OAAO,GAAG,CAAC;QACnB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,MAA2B,EAAE,EAAE;QAClD,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,2BAAmB,CAAC,SAAS;gBAC9B,OAAO,WAAW,CAAC;YACvB,KAAK,2BAAmB,CAAC,UAAU;gBAC/B,OAAO,eAAe,CAAC;YAC3B,KAAK,2BAAmB,CAAC,KAAK;gBAC1B,OAAO,OAAO,CAAC;YACnB,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,iBAAiB,CAAC;YAC7B;gBACI,OAAO,cAAc,CAAC;QAC9B,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;QAC/B,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9E,KAAK,CAAC,sDAAsD,CAAC,CAAC;YAC9D,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,WAAW,CAAC,eAAkC,CAAC,CAAC;YACtD,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxB,kBAAkB,CAAC;gBACf,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,iBAAiB;gBAC5B,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;aAClB,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QAC5C,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,KAAK,EAAE,QAAgB,EAAE,MAA2C,EAAE,EAAE;QAC/F,IAAI,CAAC;YACD,QAAQ,MAAM,EAAE,CAAC;gBACb,KAAK,SAAS;oBACV,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;oBAChC,MAAM;gBACV,KAAK,YAAY;oBACb,MAAM,kBAAkB,CAAC,QAAQ,CAAC,CAAC;oBACnC,MAAM;gBACV,KAAK,QAAQ;oBACT,IAAI,OAAO,CAAC,8CAA8C,CAAC,EAAE,CAAC;wBAC1D,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;oBACnC,CAAC;oBACD,MAAM;YACd,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,KAAK,CAAC,aAAa,MAAM,YAAY,KAAK,EAAE,CAAC,CAAC;QAClD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,cAAc,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAE9F,OAAO,CACH,6DACA,iCAAK,SAAS,EAAC,oBAAoB,aAC/B,iCAAK,SAAS,EAAC,YAAY,aACvB,6EAA8B,EAC9B,mCACI,SAAS,EAAC,iBAAiB,EAC3B,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,kCAGhC,IACP,EAEN,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAK,SAAS,EAAC,aAAa,aACxB,wDAAc,OAAO,CAAC,MAAM,SAAO,EAClC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CACpB,iCAAK,SAAS,EAAC,aAAa,aACxB,sEAAgC,EAChC,yGAAmE,IACjE,CACT,CAAC,CAAC,CAAC,CACA,gCAAK,SAAS,EAAC,aAAa,YACvB,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CACnB,iCAEI,SAAS,EAAE,eAAe,cAAc,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAC1E,OAAO,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,aAE3C,iCAAK,SAAS,EAAC,eAAe,aAC1B,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,aAAa,YAAE,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,GAAQ,EACnE,iCAAM,SAAS,EAAC,MAAM,YAAE,MAAM,CAAC,MAAM,CAAC,IAAI,GAAQ,IAChD,EACN,iCAAK,SAAS,EAAC,gBAAgB,aAC1B,MAAM,CAAC,MAAM,KAAK,2BAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,CAC/C,mCACI,SAAS,EAAC,0BAA0B,EACpC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wEACX,CAAC,CAAC,eAAe,EAAE,CAAC;wEACpB,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;oEAChD,CAAC,2BAGI,CACZ,CAAC,CAAC,CAAC,CACA,mCACI,SAAS,EAAC,wBAAwB,EAClC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wEACX,CAAC,CAAC,eAAe,EAAE,CAAC;wEACpB,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;oEAC7C,CAAC,EACD,QAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,2BAAmB,CAAC,UAAU,wBAGrD,CACZ,EACD,mCACI,SAAS,EAAC,uBAAuB,EACjC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;wEACX,CAAC,CAAC,eAAe,EAAE,CAAC;wEACpB,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;oEAC5C,CAAC,mCAGI,IACP,IACJ,EAEN,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAK,SAAS,EAAC,QAAQ,yBACX,iCAAM,SAAS,EAAE,eAAe,MAAM,CAAC,MAAM,EAAE,YAClD,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,GAC1B,IACL,EACN,iCAAK,SAAS,EAAC,WAAW,4BACV,MAAM,CAAC,MAAM,CAAC,SAAS,IACjC,EACL,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAClB,iCAAK,SAAS,EAAC,KAAK,sBAAO,MAAM,CAAC,MAAM,CAAC,GAAG,IAAO,CACtD,EACA,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,CACtB,iCAAK,SAAS,EAAC,SAAS,0BAAW,MAAM,CAAC,MAAM,CAAC,OAAO,IAAO,CAClE,EACA,MAAM,CAAC,SAAS,IAAI,CACjB,iCAAK,SAAS,EAAC,OAAO,wBAAS,MAAM,CAAC,SAAS,IAAO,CACzD,IACC,EAEL,MAAM,CAAC,MAAM,KAAK,2BAAmB,CAAC,SAAS,IAAI,CAChD,iCAAK,SAAS,EAAC,cAAc,aACzB,iCAAK,SAAS,EAAC,MAAM,aACjB,iCAAM,SAAS,EAAC,OAAO,uBAAc,EACrC,iCAAM,SAAS,EAAC,OAAO,YAAE,MAAM,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,GAAQ,IACxD,EACN,iCAAK,SAAS,EAAC,MAAM,aACjB,iCAAM,SAAS,EAAC,OAAO,2BAAkB,EACzC,iCAAM,SAAS,EAAC,OAAO,YAAE,MAAM,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,GAAQ,IAC5D,EACN,iCAAK,SAAS,EAAC,MAAM,aACjB,iCAAM,SAAS,EAAC,OAAO,yBAAgB,EACvC,iCAAM,SAAS,EAAC,OAAO,YAAE,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,GAAQ,IAC1D,IACJ,CACT,KA/EI,MAAM,CAAC,EAAE,CAgFZ,CACT,CAAC,GACA,CACT,IACC,EAEL,kBAAkB,IAAI,CACnB,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,+DAAqB,kBAAkB,CAAC,MAAM,CAAC,IAAI,IAAM,EAExD,kBAAkB,CAAC,MAAM,KAAK,2BAAmB,CAAC,SAAS,IAAI,CAC5D,6DACK,kBAAkB,CAAC,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAChE,iCAAK,SAAS,EAAC,eAAe,aAC1B,6EAAyB,kBAAkB,CAAC,KAAK,CAAC,MAAM,SAAO,EAC/D,gCAAK,SAAS,EAAC,YAAY,YACtB,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAClC,iCAAqB,SAAS,EAAC,WAAW,aACtC,gCAAK,SAAS,EAAC,WAAW,YAAE,IAAI,CAAC,IAAI,GAAO,EAC3C,IAAI,CAAC,WAAW,IAAI,CACjB,gCAAK,SAAS,EAAC,kBAAkB,YAAE,IAAI,CAAC,WAAW,GAAO,CAC7D,EACD,mCACI,SAAS,EAAC,wBAAwB,EAClC,OAAO,EAAE,GAAG,EAAE;wEACV,0CAA0C;wEAC1C,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;oEAC5C,CAAC,wBAGI,KAbH,IAAI,CAAC,IAAI,CAcb,CACT,CAAC,GACA,IACJ,CACT,EAEA,kBAAkB,CAAC,SAAS,IAAI,kBAAkB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CACxE,iCAAK,SAAS,EAAC,mBAAmB,aAC9B,iFAA6B,kBAAkB,CAAC,SAAS,CAAC,MAAM,SAAO,EACvE,gCAAK,SAAS,EAAC,gBAAgB,YAC1B,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC1C,iCAAwB,SAAS,EAAC,eAAe,aAC7C,gCAAK,SAAS,EAAC,eAAe,YAAE,QAAQ,CAAC,IAAI,GAAO,EACpD,gCAAK,SAAS,EAAC,cAAc,YAAE,QAAQ,CAAC,GAAG,GAAO,EACjD,QAAQ,CAAC,WAAW,IAAI,CACrB,gCAAK,SAAS,EAAC,sBAAsB,YAAE,QAAQ,CAAC,WAAW,GAAO,CACrE,KALK,QAAQ,CAAC,GAAG,CAMhB,CACT,CAAC,GACA,IACJ,CACT,EAEA,kBAAkB,CAAC,OAAO,IAAI,kBAAkB,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CACpE,iCAAK,SAAS,EAAC,iBAAiB,aAC5B,+EAA2B,kBAAkB,CAAC,OAAO,CAAC,MAAM,SAAO,EACnE,gCAAK,SAAS,EAAC,cAAc,YACxB,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CACtC,iCAAuB,SAAS,EAAC,aAAa,aAC1C,gCAAK,SAAS,EAAC,aAAa,YAAE,MAAM,CAAC,IAAI,GAAO,EAC/C,MAAM,CAAC,WAAW,IAAI,CACnB,gCAAK,SAAS,EAAC,oBAAoB,YAAE,MAAM,CAAC,WAAW,GAAO,CACjE,EACA,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAChD,iCAAK,SAAS,EAAC,aAAa,4BACZ,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAC1D,CACT,KATK,MAAM,CAAC,IAAI,CAUf,CACT,CAAC,GACA,IACJ,CACT,IACF,CACN,IACC,CACT,IACC,EAEL,aAAa,IAAI,CACd,gCAAK,SAAS,EAAC,eAAe,YAC1B,iCAAK,SAAS,EAAC,OAAO,aAClB,iCAAK,SAAS,EAAC,cAAc,aACzB,4DAAuB,EACvB,mCACI,SAAS,EAAC,YAAY,EACtB,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,uBAGjC,IACP,EAEN,iCAAK,SAAS,EAAC,YAAY,aACvB,iCAAK,SAAS,EAAC,YAAY,aACvB,8DAA4B,EAC5B,kCACI,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,eAAe,CAAC,IAAI,IAAI,EAAE,EACjC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;wDAChC,GAAG,eAAe;wDAClB,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK;qDACvB,CAAC,EACF,WAAW,EAAC,yBAAyB,GACvC,IACA,EAEN,iCAAK,SAAS,EAAC,YAAY,aACvB,0DAAwB,EACxB,oCACI,KAAK,EAAE,eAAe,CAAC,SAAS,EAChC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;wDAChC,GAAG,eAAe;wDAClB,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,KAAY;qDACnC,CAAC,aAEF,mCAAQ,KAAK,EAAC,iBAAiB,gCAAyB,EACxD,mCAAQ,KAAK,EAAC,OAAO,sBAAe,EACpC,mCAAQ,KAAK,EAAC,MAAM,qBAAc,IAC7B,IACP,EAEL,eAAe,CAAC,SAAS,KAAK,iBAAiB,IAAI,eAAe,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,CACvF,iCAAK,SAAS,EAAC,YAAY,aACvB,6DAA2B,EAC3B,kCACI,IAAI,EAAC,KAAK,EACV,KAAK,EAAE,eAAe,CAAC,GAAG,IAAI,EAAE,EAChC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;wDAChC,GAAG,eAAe;wDAClB,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK;qDACtB,CAAC,EACF,WAAW,EAAC,6BAA6B,GAC3C,IACA,CACT,CAAC,CAAC,CAAC,CACA,iCAAK,SAAS,EAAC,YAAY,aACvB,0DAAwB,EACxB,kCACI,IAAI,EAAC,MAAM,EACX,KAAK,EAAE,eAAe,CAAC,OAAO,IAAI,EAAE,EACpC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;wDAChC,GAAG,eAAe;wDAClB,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK;qDAC1B,CAAC,EACF,WAAW,EAAC,gBAAgB,GAC9B,IACA,CACT,EAED,gCAAK,SAAS,EAAC,YAAY,YACvB,8CACI,kCACI,IAAI,EAAC,UAAU,EACf,OAAO,EAAE,eAAe,CAAC,OAAO,EAChC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;4DAChC,GAAG,eAAe;4DAClB,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO;yDAC5B,CAAC,GACJ,qBAEE,GACN,EAEN,gCAAK,SAAS,EAAC,YAAY,YACvB,8CACI,kCACI,IAAI,EAAC,UAAU,EACf,OAAO,EAAE,eAAe,CAAC,SAAS,EAClC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC;4DAChC,GAAG,eAAe;4DAClB,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO;yDAC9B,CAAC,GACJ,oCAEE,GACN,IACJ,EAEN,iCAAK,SAAS,EAAC,cAAc,aACzB,mCACI,SAAS,EAAC,mBAAmB,EAC7B,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,uBAGjC,EACT,mCACI,SAAS,EAAC,iBAAiB,EAC3B,OAAO,EAAE,eAAe,2BAGnB,IACP,IACJ,GACJ,CACT,IACC,EAEN,4CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAmXP,GAAS,IACP,CACN,CAAC;AACN,CAAC,CAAC;AA9vBW,QAAA,gBAAgB,oBA8vB3B"}