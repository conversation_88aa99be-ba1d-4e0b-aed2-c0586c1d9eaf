{"version": 3, "file": "MCPServerManager.js", "sourceRoot": "", "sources": ["../../../src/ui/components/MCPServerManager.tsx"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;GASG;AAEH,iCAAmD;AACnD,2CAOyB;AAiBlB,MAAM,gBAAgB,GAAoC,CAAC,EAC9D,MAAM,EACN,aAAa,EACb,eAAe,EACf,eAAe,EAClB,EAAE,EAAE;IACD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAA0B,IAAI,GAAG,EAAE,CAAC,CAAC;IAC3E,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC1D,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IAE1E,IAAA,iBAAS,EAAC,GAAG,EAAE;QACX,WAAW,EAAE,CAAC;QAEd,4BAA4B;QAC5B,MAAM,aAAa,GAAG,CAAC,KAAmB,EAAE,EAAE;YAC1C,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;YAC3B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,oBAAoB;oBACrB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;oBACrD,MAAM;gBACV,KAAK,aAAa;oBACd,WAAW,EAAE,CAAC,CAAC,qBAAqB;oBACpC,MAAM;gBACV,KAAK,eAAe;oBAChB,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC/B,MAAM;YACd,CAAC;QACL,CAAC,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAClD,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC3B,IAAI,CAAC;YACD,UAAU,CAAC,IAAI,CAAC,CAAC;YACjB,MAAM,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;gBAAS,CAAC;YACP,UAAU,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,CAAC,QAAgB,EAAE,MAAuB,EAAE,EAAE;QACrE,UAAU,CAAC,IAAI,CAAC,EAAE;YACd,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YACjC,MAAM,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACrB,GAAG,UAAU;oBACb,MAAM;iBACT,CAAC,CAAC;YACP,CAAC;YACD,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,QAAgB,EAAE,EAAE;QACtC,UAAU,CAAC,IAAI,CAAC,EAAE;YACd,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;YACjC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC5B,OAAO,UAAU,CAAC;QACtB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,KAAK,EAAE,QAAgB,EAAE,OAAgB,EAAE,EAAE;QACpE,IAAI,CAAC;YACD,MAAM,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,iBAAiB;gBACvB,QAAQ;gBACR,OAAO;aACV,CAAC,CAAC;YAEH,eAAe,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QACpD,IAAI,CAAC;YACD,MAAM,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,mBAAmB;gBACzB,QAAQ;aACX,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACvD,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,KAAK,EAAE,QAAgB,EAAE,EAAE;QAClD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,MAAM,SAAS,GAAG,MAAM,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,EAAE;YACrD,MAAM,CAAC,WAAW,CAAC;gBACf,IAAI,EAAE,eAAe;gBACrB,OAAO,EAAE,+CAA+C,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI;gBAClF,QAAQ,EAAE,OAAO;aACpB,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC;gBACD,MAAM,CAAC,WAAW,CAAC;oBACf,IAAI,EAAE,iBAAiB;oBACvB,QAAQ;iBACX,CAAC,CAAC;gBAEH,eAAe,EAAE,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,MAA2B,EAAU,EAAE;QAC1D,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,2BAAmB,CAAC,SAAS;gBAC9B,OAAO,GAAG,CAAC;YACf,KAAK,2BAAmB,CAAC,UAAU;gBAC/B,OAAO,IAAI,CAAC;YAChB,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,GAAG,CAAC;YACf,KAAK,2BAAmB,CAAC,KAAK;gBAC1B,OAAO,GAAG,CAAC;YACf,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,IAAI,CAAC;YAChB;gBACI,OAAO,GAAG,CAAC;QACnB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,MAA2B,EAAU,EAAE;QAC3D,QAAQ,MAAM,EAAE,CAAC;YACb,KAAK,2BAAmB,CAAC,SAAS;gBAC9B,OAAO,SAAS,CAAC,CAAC,QAAQ;YAC9B,KAAK,2BAAmB,CAAC,UAAU,CAAC;YACpC,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,SAAS,CAAC,CAAC,SAAS;YAC/B,KAAK,2BAAmB,CAAC,YAAY;gBACjC,OAAO,SAAS,CAAC,CAAC,OAAO;YAC7B,KAAK,2BAAmB,CAAC,KAAK;gBAC1B,OAAO,SAAS,CAAC,CAAC,MAAM;YAC5B;gBACI,OAAO,SAAS,CAAC;QACzB,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,UAAsB,EAAE,EAAE;QAChD,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;QAEjE,OAAO,CACH,iCAAqB,SAAS,EAAC,uBAAuB,aAClD,iCAAK,SAAS,EAAC,eAAe,aAC1B,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAK,SAAS,EAAC,cAAc,aACzB,iCAAM,SAAS,EAAC,aAAa,YAAE,MAAM,CAAC,IAAI,IAAI,IAAI,GAAQ,EAC1D,yCAAK,MAAM,CAAC,IAAI,GAAM,EACrB,MAAM,CAAC,SAAS,IAAI,iCAAM,SAAS,EAAC,gBAAgB,yBAAgB,IACnE,EACN,8BAAG,SAAS,EAAC,oBAAoB,YAAE,MAAM,CAAC,WAAW,GAAK,EAC1D,iCAAK,SAAS,EAAC,cAAc,aACzB,kCAAM,SAAS,EAAC,MAAM,aAClB,iCAAM,SAAS,EAAC,WAAW,6BAAU,EACpC,MAAM,CAAC,SAAS,cACd,EACP,kCAAM,SAAS,EAAC,MAAM,aAClB,iCAAM,SAAS,EAAC,WAAW,6BAAU,EACpC,MAAM,CAAC,aAAa,kBAClB,EACP,kCAAM,SAAS,EAAC,MAAM,aAClB,iCAAM,SAAS,EAAC,WAAW,6BAAU,EACpC,MAAM,CAAC,WAAW,gBAChB,IACL,IACJ,EACN,iCAAK,SAAS,EAAC,eAAe,aAC1B,kCACI,SAAS,EAAC,cAAc,EACxB,KAAK,EAAE,EAAE,KAAK,EAAE,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,aAE9C,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,OAAG,MAAM,CAAC,MAAM,IAC1C,EACN,MAAM,CAAC,SAAS,IAAI,CACjB,iCAAK,SAAS,EAAC,eAAe,EAAC,KAAK,EAAE,MAAM,CAAC,SAAS,8BAC9C,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,WACnC,CACT,IACC,IACJ,EAEN,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,mCAAO,SAAS,EAAC,eAAe,aAC5B,kCACI,IAAI,EAAC,UAAU,EACf,OAAO,EAAE,MAAM,CAAC,OAAO,EACvB,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAClE,EACF,iCAAM,SAAS,EAAC,qBAAqB,GAAQ,IACzC,EAER,mCACI,SAAS,EAAC,6BAA6B,EACvC,OAAO,EAAE,GAAG,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC,EAC9C,QAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,2BAAmB,CAAC,UAAU,EAC1D,KAAK,EAAC,iBAAiB,kCAGlB,EAET,mCACI,SAAS,EAAC,eAAe,EACzB,OAAO,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAC3C,KAAK,EAAC,cAAc,qCAGf,EAER,CAAC,MAAM,CAAC,SAAS,IAAI,CAClB,mCACI,SAAS,EAAC,0BAA0B,EACpC,OAAO,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,EAC5C,KAAK,EAAC,eAAe,0CAGhB,CACZ,IACC,EAEL,cAAc,KAAK,MAAM,CAAC,EAAE,IAAI,CAC7B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,4DAAuB,EACvB,mCACI,SAAS,EAAC,WAAW,EACrB,OAAO,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,uBAGjC,IACP,EAEN,iCAAK,SAAS,EAAC,iBAAiB,aAC5B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,2DAAsB,EACtB,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,2BAAkB,EAChD,iCAAM,SAAS,EAAC,cAAc,YAAE,MAAM,CAAC,SAAS,GAAQ,IACtD,EACN,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,yBAAgB,EAC9C,iCAAM,SAAS,EAAC,cAAc,YAAE,MAAM,CAAC,OAAO,IAAI,SAAS,GAAQ,IACjE,EACN,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,yBAAgB,EAC9C,kCAAM,SAAS,EAAC,cAAc,aAAE,MAAM,CAAC,OAAO,UAAU,IACtD,EACN,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,4BAAmB,EACjD,iCAAM,SAAS,EAAC,cAAc,YAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAQ,IACrE,IACJ,IACJ,EAEL,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CACjB,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,gEAAsB,KAAK,CAAC,MAAM,SAAO,EACzC,iCAAK,SAAS,EAAC,YAAY,aACtB,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAC3B,iCAAqB,SAAS,EAAC,WAAW,aACtC,iCAAM,SAAS,EAAC,WAAW,YAAE,IAAI,CAAC,IAAI,GAAQ,EAC9C,iCAAM,SAAS,EAAC,kBAAkB,YAAE,IAAI,CAAC,WAAW,GAAQ,EAC3D,IAAI,CAAC,SAAS,IAAI,CACf,iCAAM,SAAS,EAAE,mBAAmB,IAAI,CAAC,SAAS,EAAE,YAC/C,IAAI,CAAC,SAAS,GACZ,CACV,KAPK,IAAI,CAAC,IAAI,CAQb,CACT,CAAC,EACD,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CACjB,iCAAK,SAAS,EAAC,YAAY,kBACrB,KAAK,CAAC,MAAM,GAAG,CAAC,mBAChB,CACT,IACC,IACJ,CACT,EAEA,MAAM,CAAC,aAAa,IAAI,CACrB,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,6DAAwB,EACxB,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,gCAAuB,EACrD,iCAAM,SAAS,EAAC,cAAc,YACzB,MAAM,CAAC,aAAa,CAAC,cAAc,EAAE,GACnC,IACL,EACL,MAAM,CAAC,MAAM,IAAI,CACd,iCAAK,SAAS,EAAC,aAAa,aACxB,iCAAM,SAAS,EAAC,cAAc,wBAAe,EAC7C,kCAAM,SAAS,EAAC,cAAc,aACzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAC9B,IACL,CACT,IACC,CACT,IACC,IACJ,CACT,KA7JK,MAAM,CAAC,EAAE,CA8Jb,CACT,CAAC;IACN,CAAC,CAAC;IAEF,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CACH,iCAAK,SAAS,EAAC,aAAa,aACxB,gCAAK,SAAS,EAAC,iBAAiB,GAAO,EACvC,mEAA6B,IAC3B,CACT,CAAC;IACN,CAAC;IAED,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpF,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAEtF,OAAO,CACH,iCAAK,SAAS,EAAC,oBAAoB,aAC/B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,sEAAiC,EACjC,oCACI,SAAS,EAAC,iBAAiB,EAC3B,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAErC,iCAAM,SAAS,EAAC,UAAU,uBAAS,2BAE9B,IACP,EAEL,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,CAC1B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,8DAAyB,EACzB,8BAAG,SAAS,EAAC,qBAAqB,iFAE9B,EACJ,gCAAK,SAAS,EAAC,cAAc,YACxB,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,GAC7D,IACJ,CACT,EAEA,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,CAC3B,iCAAK,SAAS,EAAC,gBAAgB,aAC3B,8DAAyB,EACzB,8BAAG,SAAS,EAAC,qBAAqB,yEAE9B,EACJ,gCAAK,SAAS,EAAC,cAAc,YACxB,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,GAC9D,IACJ,CACT,EAEA,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,CAC7B,iCAAK,SAAS,EAAC,aAAa,aACxB,gCAAK,SAAS,EAAC,YAAY,6BAAS,EACpC,iEAA4B,EAC5B,wGAAkE,EAClE,mCACI,SAAS,EAAC,iBAAiB,EAC3B,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,sCAGhC,IACP,CACT,EAEA,aAAa,IAAI,CACd,uBAAC,eAAe,IACZ,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC,EACtC,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACxB,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBACxB,aAAa,EAAE,CAAC,QAAQ,CAAC,CAAC;oBAC1B,WAAW,EAAE,CAAC;gBAClB,CAAC,GACH,CACL,IACC,CACT,CAAC;AACN,CAAC,CAAC;AAzYW,QAAA,gBAAgB,oBAyY3B;AAEF,+DAA+D;AAC/D,MAAM,eAAe,GAIhB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;IACxC,4CAA4C;IAC5C,OAAO,CACH,gCAAK,SAAS,EAAC,gBAAgB,YAC3B,iCAAK,SAAS,EAAC,cAAc,aACzB,qEAAgC,EAChC,iFAA2C,EAC3C,mCAAQ,OAAO,EAAE,OAAO,sBAAgB,IACtC,GACJ,CACT,CAAC;AACN,CAAC,CAAC"}