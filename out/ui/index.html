<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSP_SOURCE}} 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net https://unpkg.com; script-src 'nonce-{{NONCE}}' https://unpkg.com; font-src {{CSP_SOURCE}} data: https://fonts.gstatic.com; img-src {{CSP_SOURCE}} data: https:; connect-src https:;">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aizen AI Chat</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{CSS_URI}}">
    <link rel="stylesheet" href="{{CHAT_CSS_URI}}">
</head>
<body class="{{THEME_CLASS}}">
    <div id="root">
        <!-- Top Navigation Bar -->
        <header class="top-nav glass">
            <div class="nav-left">
                <div class="user-avatar glass">
                    <i data-lucide="user-circle"></i>
                </div>
                <div class="nav-tabs">
                    <button type="button" class="tab-btn active" data-tab="actions">
                        <i data-lucide="zap"></i>
                        <span>Actions</span>
                    </button>
                    <button type="button" class="tab-btn" data-tab="flows">
                        <i data-lucide="workflow"></i>
                        <span>Flows</span>
                    </button>
                </div>
            </div>
            <div class="nav-right">
                <button type="button" class="nav-btn glass" title="New Chat" data-action="new-chat">
                    <i data-lucide="plus"></i>
                </button>
                <button type="button" class="nav-btn glass" title="Query History" data-action="history">
                    <i data-lucide="clock"></i>
                </button>
                <button type="button" class="nav-btn glass" title="MCP Store" data-action="mcp-store">
                    <i data-lucide="shopping-bag"></i>
                </button>
                <button type="button" class="nav-btn glass" title="Settings" data-action="settings">
                    <i data-lucide="settings"></i>
                </button>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="main-content">
            <div class="chat-container custom-scroll">
                <div class="chat-messages" id="chatMessages">
                    <!-- Welcome Message -->
                    <div class="welcome-message glass">
                        <div class="welcome-icon">
                            <i data-lucide="brain-circuit"></i>
                        </div>
                        <h2>Welcome to Aizen AI</h2>
                        <p>Your revolutionary AI assistant with advanced reasoning and swarm intelligence</p>
                        <div class="welcome-features">
                            <div class="feature-item">
                                <i data-lucide="brain"></i>
                                <span>Advanced Reasoning</span>
                            </div>
                            <div class="feature-item">
                                <i data-lucide="network"></i>
                                <span>Swarm Intelligence</span>
                            </div>
                            <div class="feature-item">
                                <i data-lucide="rocket"></i>
                                <span>Self-Improvement</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Chat Input Area -->
        <footer class="chat-input-area glass">
            <div class="input-container">
                <div class="input-left">
                    <button type="button" class="input-btn glass" title="Upload Media">
                        <i data-lucide="paperclip"></i>
                    </button>
                    <div class="mode-selector">
                        <button type="button" class="mode-btn glass" id="modeButton">
                            <i data-lucide="wand-2"></i>
                            <span class="mode-text">Auto</span>
                            <i data-lucide="chevron-down"></i>
                        </button>
                        <div class="mode-dropdown glass" id="modeDropdown">
                            <div class="mode-option active" data-mode="auto">
                                <i data-lucide="wand-2"></i>
                                <span>Auto Mode</span>
                            </div>
                            <div class="mode-option" data-mode="research">
                                <i data-lucide="search"></i>
                                <span>Research Mode</span>
                            </div>
                            <div class="mode-option" data-mode="debug">
                                <i data-lucide="bug"></i>
                                <span>Debug Mode</span>
                            </div>
                            <div class="mode-option" data-mode="deep">
                                <i data-lucide="brain"></i>
                                <span>Deep Mode</span>
                            </div>
                            <div class="mode-option" data-mode="mcp">
                                <i data-lucide="network"></i>
                                <span>MCP Mode</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="input-center">
                    <input type="text" 
                           class="chat-input glass" 
                           placeholder="Ask anything with context @ or / for your custom agents"
                           id="chatInput">
                </div>
                <div class="input-right">
                    <div class="agent-mode-selector">
                        <button type="button" class="agent-mode-btn glass" id="agentModeButton">
                            <i data-lucide="message-circle-question"></i>
                            <span class="agent-mode-text">Ask</span>
                            <i data-lucide="chevron-down"></i>
                        </button>
                        <div class="agent-mode-dropdown glass" id="agentModeDropdown">
                            <div class="agent-mode-option active" data-mode="ask">
                                <i data-lucide="message-circle-question"></i>
                                <span>Ask Mode</span>
                            </div>
                            <div class="agent-mode-option" data-mode="agent">
                                <i data-lucide="brain-circuit"></i>
                                <span>Agent Mode</span>
                            </div>
                            <div class="agent-mode-option" data-mode="god">
                                <i data-lucide="crown"></i>
                                <span>God Mode</span>
                            </div>
                        </div>
                    </div>
                    <div class="auto-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoToggle">
                            <span class="toggle-slider glass"></span>
                        </label>
                        <span class="toggle-label">Auto</span>
                    </div>
                    <button type="button" class="send-btn glass" id="sendButton">
                        <i data-lucide="send"></i>
                    </button>
                </div>
            </div>
        </footer>
    </div>
    <script nonce="{{NONCE}}" src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script nonce="{{NONCE}}">
        // VS Code webview API - CRITICAL for extension communication
        const vscode = acquireVsCodeApi();
        console.log('✅ VS Code API acquired:', typeof vscode);
        window.vscode = vscode; // Make it globally available
    </script>
    <script nonce="{{NONCE}}" src="{{SCRIPT_URI}}"></script>
    <script nonce="{{NONCE}}">
        // Initialize Lucide icons when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });
    </script>
</body>
</html>
