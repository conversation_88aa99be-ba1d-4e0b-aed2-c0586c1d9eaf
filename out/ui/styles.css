/* Modern Minimal Design System - 2025 */
:root {
    /* Light Mode Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-surface: #ffffff;

    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-inverse: #ffffff;

    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-focus: #3b82f6;

    --accent-blue: #3b82f6;
    --accent-blue-light: #dbeafe;
    --accent-green: #10b981;
    --accent-green-light: #d1fae5;
    --accent-red: #ef4444;
    --accent-red-light: #fee2e2;
    --accent-yellow: #f59e0b;
    --accent-yellow-light: #fef3c7;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows - Minimal and subtle */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Mode Support */
.vscode-dark,
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-surface: #1e293b;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;

    --border-primary: #334155;
    --border-secondary: #475569;
    --border-focus: #60a5fa;

    --accent-blue: #60a5fa;
    --accent-blue-light: #1e3a8a;
    --accent-green: #34d399;
    --accent-green-light: #064e3b;
    --accent-red: #f87171;
    --accent-red-light: #7f1d1d;
    --accent-yellow: #fbbf24;
    --accent-yellow-light: #78350f;

    /* Adjust shadows for dark mode */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.vscode-light {
    --bg-primary: #ffffff;
    --bg-secondary: #f3f3f3;
    --bg-tertiary: #e8e8e8;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-accent: #0078d4;
    --border-color: #d1d1d1;
}

.vscode-high-contrast {
    --bg-primary: #000000;
    --bg-secondary: #000000;
    --bg-tertiary: #000000;
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --text-accent: #ffff00;
    --border-color: #ffffff;
}

/* Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    position: relative;
    margin: 0;
    padding: 0;
}

/* Clean minimal background - no effects */

#root {
    height: 100vh;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

/* Modern Minimal Card Style */
.glass {
    background: var(--bg-surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.glass:hover {
    background: var(--bg-secondary);
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-md);
}

/* Custom Scrollbar */
.custom-scroll {
    scrollbar-width: thin;
    scrollbar-color: var(--border-secondary) transparent;
}

.custom-scroll::-webkit-scrollbar {
    width: 6px;
}

.custom-scroll::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scroll::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 3px;
    transition: background var(--transition-fast);
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
    background: var(--border-focus);
}

/* Top Navigation Styles */
.top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    margin: var(--space-4);
    margin-bottom: 0;
    position: relative;
    z-index: 10;
    border-bottom: 1px solid var(--border-primary);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-accent);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.user-avatar:hover {
    transform: scale(1.05);
}

.nav-tabs {
    display: flex;
    gap: var(--space-2);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: transparent;
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--glass-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.tab-btn:hover::before {
    opacity: 1;
}

.tab-btn.active {
    color: var(--text-accent);
    background: var(--glass-active);
}

.tab-btn .hgi-stroke {
    font-size: 1em;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.nav-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.nav-btn:hover {
    color: var(--text-accent);
    transform: translateY(-1px);
}

.nav-btn .hgi-stroke {
    font-size: 1.1em;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    padding: 0 var(--space-4);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
    margin-bottom: var(--space-4);
}

.chat-messages {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    min-height: 100%;
    justify-content: flex-end;
}

.welcome-message {
    text-align: center;
    padding: var(--space-8);
    margin: auto;
    max-width: 400px;
    animation: fadeInUp 0.6s ease-out;
}

.welcome-icon {
    margin-bottom: var(--space-4);
    color: var(--text-accent);
    animation: pulse 2s ease-in-out infinite;
}

.welcome-icon .hgi-stroke {
    font-size: 3rem;
}

.welcome-message h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--text-primary);
}

.welcome-message p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.welcome-features {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4);
    border-radius: var(--radius-md);
    background: var(--glass-secondary);
    transition: all var(--transition-normal);
    cursor: pointer;
    min-width: 100px;
}

.feature-item:hover {
    background: var(--glass-hover);
    transform: translateY(-2px);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--text-accent);
}

.feature-item span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: 500;
    text-align: center;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Icon Styling */
.hgi-stroke {
    font-size: 1.2em;
    line-height: 1;
}

.user-avatar .hgi-stroke {
    font-size: 1.5em;
}

/* Chat Input Area Styles */
.chat-input-area {
    margin: var(--space-4);
    margin-top: 0;
    padding: var(--space-4);
    position: relative;
    z-index: 10;
}

.input-container {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    width: 100%;
}

.input-left {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.input-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.input-btn:hover {
    color: var(--text-accent);
    transform: translateY(-1px);
}

.mode-selector {
    position: relative;
}

.mode-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 100px;
    justify-content: space-between;
}

.mode-dropdown {
    position: absolute;
    top: calc(100% + var(--space-2));
    left: 0;
    min-width: 150px;
    border-radius: var(--radius-md);
    padding: var(--space-2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: 100;
}

.mode-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mode-option {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.mode-option:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
}

.mode-option.active {
    background: var(--glass-active);
    color: var(--text-accent);
}

.mode-option i {
    font-size: 1em;
    opacity: 0.8;
}

.mode-option.active i {
    opacity: 1;
    color: var(--text-accent);
}

.input-center {
    flex: 1;
}

.chat-input {
    width: 100%;
    padding: var(--space-4) var(--space-6);
    border: none;
    border-radius: var(--radius-xl);
    background: var(--glass-secondary);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    outline: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.chat-input::placeholder {
    color: var(--text-secondary);
}

.chat-input:focus {
    background: var(--glass-primary);
    box-shadow: 0 0 0 2px var(--text-accent);
}

.input-right {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.agent-mode-selector {
    position: relative;
}

.agent-mode-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-width: 80px;
    justify-content: space-between;
}

.agent-mode-dropdown {
    position: absolute;
    top: calc(100% + var(--space-2));
    right: 0;
    min-width: 120px;
    border-radius: var(--radius-md);
    padding: var(--space-2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: 100;
}

.agent-mode-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.agent-mode-option {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.agent-mode-option:hover {
    background: var(--glass-hover);
    color: var(--text-primary);
}

.agent-mode-option.active {
    background: var(--glass-active);
    color: var(--text-accent);
}

.agent-mode-option i {
    font-size: 1em;
    opacity: 0.8;
}

.agent-mode-option.active i {
    opacity: 1;
    color: var(--text-accent);
}

.auto-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 24px;
    transition: all var(--transition-normal);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: var(--text-primary);
    border-radius: 50%;
    transition: all var(--transition-normal);
}

input:checked + .toggle-slider {
    background: var(--text-accent);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.send-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 50%;
    background: var(--text-accent);
    color: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-elevated);
}

.send-btn:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-left {
        gap: var(--space-4);
    }

    .nav-right {
        gap: var(--space-2);
    }

    .input-container {
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .input-center {
        order: 3;
        flex-basis: 100%;
    }
}
