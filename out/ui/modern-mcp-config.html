<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline'; img-src data: vscode-resource:; form-action 'self';">
    <title>MCP Configuration - Aizen AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1117;
            color: #f0f6fc;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem 0;
            border-bottom: 1px solid #21262d;
        }

        .logo {
            width: 48px;
            height: 48px;
            margin-bottom: 1rem;
            filter: brightness(0) invert(1);
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #58a6ff, #79c0ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #8b949e;
            font-size: 1.1rem;
        }

        .section {
            background: #161b22;
            border: 1px solid #21262d;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .section:hover {
            border-color: #30363d;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #f0f6fc;
            display: flex;
            align-items: center;
        }

        .section-icon {
            width: 24px;
            height: 24px;
            margin-right: 1rem;
            color: #58a6ff;
        }

        .section-description {
            color: #8b949e;
            margin-bottom: 1.5rem;
        }

        .server-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .server-card {
            background: #0d1117;
            border: 1px solid #21262d;
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .server-card:hover {
            border-color: #58a6ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(88, 166, 255, 0.1);
        }

        .server-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .server-name {
            font-weight: 600;
            color: #f0f6fc;
            font-size: 1.1rem;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-active {
            background: rgba(46, 160, 67, 0.2);
            color: #3fb950;
            border: 1px solid #3fb950;
        }

        .status-inactive {
            background: rgba(248, 81, 73, 0.2);
            color: #f85149;
            border: 1px solid #f85149;
        }

        .server-description {
            color: #8b949e;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .server-details {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .detail-tag {
            background: rgba(88, 166, 255, 0.2);
            color: #58a6ff;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .server-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .btn-primary {
            background: #238636;
            color: white;
        }

        .btn-primary:hover {
            background: #2ea043;
        }

        .btn-secondary {
            background: transparent;
            color: #58a6ff;
            border: 1px solid #58a6ff;
        }

        .btn-secondary:hover {
            background: rgba(88, 166, 255, 0.1);
        }

        .btn-danger {
            background: transparent;
            color: #f85149;
            border: 1px solid #f85149;
        }

        .btn-danger:hover {
            background: rgba(248, 81, 73, 0.1);
        }

        .add-server-card {
            border: 2px dashed #21262d;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-server-card:hover {
            border-color: #58a6ff;
            background: rgba(88, 166, 255, 0.05);
        }

        .add-icon {
            width: 48px;
            height: 48px;
            color: #58a6ff;
            margin-bottom: 1rem;
        }

        .add-text {
            color: #8b949e;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .add-subtext {
            color: #6e7681;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #161b22;
            border: 1px solid #21262d;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #58a6ff;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #8b949e;
            font-size: 0.9rem;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #161b22;
            border: 1px solid #21262d;
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: #8b949e;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #f0f6fc;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            background: #0d1117;
            border: 1px solid #21262d;
            border-radius: 6px;
            color: #f0f6fc;
            font-size: 0.9rem;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #58a6ff;
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .server-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 MCP Configuration</h1>
            <p class="subtitle">Manage your Model Context Protocol servers</p>
        </header>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="activeServers">2</div>
                <div class="stat-label">Active Servers</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTools">12</div>
                <div class="stat-label">Available Tools</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalResources">8</div>
                <div class="stat-label">Resources</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPrompts">5</div>
                <div class="stat-label">Prompts</div>
            </div>
        </div>

        <!-- Built-in Servers -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="section-icon">🔧</span>
                    Built-in Servers
                </h2>
            </div>
            <p class="section-description">Pre-configured MCP servers with built-in API keys</p>

            <div class="server-grid">
                <div class="server-card">
                    <div class="server-header">
                        <div class="server-name">Exa AI Search</div>
                        <span class="status-badge status-active">Active</span>
                    </div>
                    <div class="server-description">
                        Advanced web search and content discovery with AI-powered insights
                    </div>
                    <div class="server-details">
                        <span class="detail-tag">Web Search</span>
                        <span class="detail-tag">Research</span>
                        <span class="detail-tag">Built-in</span>
                    </div>
                    <div class="server-actions">
                        <button class="btn btn-secondary" onclick="testServer('exa')">Test</button>
                        <button class="btn btn-primary" onclick="configureServer('exa')">Configure</button>
                    </div>
                </div>

                <div class="server-card">
                    <div class="server-header">
                        <div class="server-name">Firecrawl</div>
                        <span class="status-badge status-active">Active</span>
                    </div>
                    <div class="server-description">
                        Web scraping and content extraction with advanced parsing capabilities
                    </div>
                    <div class="server-details">
                        <span class="detail-tag">Web Scraping</span>
                        <span class="detail-tag">Content</span>
                        <span class="detail-tag">Built-in</span>
                    </div>
                    <div class="server-actions">
                        <button class="btn btn-secondary" onclick="testServer('firecrawl')">Test</button>
                        <button class="btn btn-primary" onclick="configureServer('firecrawl')">Configure</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- External Servers -->
        <section class="section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="section-icon">🌐</span>
                    External Servers
                </h2>
                <button class="btn btn-primary" onclick="addExternalServer()">
                    + Add Server
                </button>
            </div>
            <p class="section-description">Custom MCP servers configured by you</p>

            <div class="server-grid">
                <div class="add-server-card" onclick="addExternalServer()">
                    <div class="add-icon">+</div>
                    <div class="add-text">Add External Server</div>
                    <div class="add-subtext">Connect to custom MCP servers</div>
                </div>
            </div>
        </section>
    </div>

    <!-- Add Server Modal -->
    <div id="addServerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Add External MCP Server</h3>
                <button type="button" class="close-btn" onclick="closeModal()">&times;</button>
            </div>

            <form id="serverForm">
                <div class="form-group">
                    <label class="form-label">Server Name</label>
                    <input type="text" class="form-input" id="serverName" placeholder="My Custom Server" required>
                </div>

                <div class="form-group">
                    <label class="form-label">Transport Type</label>
                    <select class="form-select" id="transportType" onchange="updateTransportFields()">
                        <option value="stdio">STDIO</option>
                        <option value="http">HTTP</option>
                        <option value="sse">Server-Sent Events</option>
                    </select>
                </div>

                <div id="stdioFields">
                    <div class="form-group">
                        <label class="form-label">Command</label>
                        <input type="text" class="form-input" id="command" placeholder="node server.js">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Arguments (one per line)</label>
                        <textarea class="form-input" id="args" rows="3" placeholder="--port 3000&#10;--verbose"></textarea>
                    </div>
                </div>

                <div id="httpFields" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">URL</label>
                        <input type="url" class="form-input" id="url" placeholder="http://localhost:3000">
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea class="form-input" id="description" rows="2" placeholder="What does this server do?"></textarea>
                </div>

                <div class="server-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Server</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Modern MCP Configuration JavaScript

        function addExternalServer() {
            document.getElementById('addServerModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('addServerModal').style.display = 'none';
        }

        function updateTransportFields() {
            const transportType = document.getElementById('transportType').value;
            const stdioFields = document.getElementById('stdioFields');
            const httpFields = document.getElementById('httpFields');

            if (transportType === 'stdio') {
                stdioFields.style.display = 'block';
                httpFields.style.display = 'none';
            } else {
                stdioFields.style.display = 'none';
                httpFields.style.display = 'block';
            }
        }

        function testServer(serverId) {
            // Send message to VS Code extension
            if (typeof vscode !== 'undefined') {
                vscode.postMessage({
                    command: 'testServer',
                    serverId: serverId
                });
            }
        }

        function configureServer(serverId) {
            if (typeof vscode !== 'undefined') {
                vscode.postMessage({
                    command: 'configureServer',
                    serverId: serverId
                });
            }
        }

        // Handle form submission
        document.getElementById('serverForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('serverName').value,
                transport: document.getElementById('transportType').value,
                description: document.getElementById('description').value
            };

            if (formData.transport === 'stdio') {
                formData.command = document.getElementById('command').value;
                formData.args = document.getElementById('args').value.split('\n').filter(arg => arg.trim());
            } else {
                formData.url = document.getElementById('url').value;
            }

            if (typeof vscode !== 'undefined') {
                vscode.postMessage({
                    command: 'addExternalServer',
                    serverConfig: formData
                });
            }

            closeModal();
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('addServerModal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Modern MCP Configuration loaded');

            // Request current server status
            if (typeof vscode !== 'undefined') {
                vscode.postMessage({
                    command: 'getServerStatus'
                });
            }
        });

        // Handle messages from VS Code
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'updateStats':
                    document.getElementById('activeServers').textContent = message.stats.activeServers;
                    document.getElementById('totalTools').textContent = message.stats.totalTools;
                    document.getElementById('totalResources').textContent = message.stats.totalResources;
                    document.getElementById('totalPrompts').textContent = message.stats.totalPrompts;
                    break;

                case 'serverAdded':
                    console.log('✅ Server added successfully');
                    // Refresh the page or update the UI
                    break;

                case 'error':
                    console.error('❌ Error:', message.error);
                    break;
            }
        });
    </script>
</body>
</html>
