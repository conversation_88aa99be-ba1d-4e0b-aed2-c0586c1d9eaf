/* Chat Messages and Animations */

/* Ripple Animation */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Chat Message Styles */
.chat-message {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease-out;
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.75rem;
    max-width: 80%;
    word-wrap: break-word;
}

.chat-message.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.chat-message.user {
    margin-left: auto;
    margin-right: 1rem;
    background: var(--text-accent);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.chat-message.assistant {
    margin-left: 1rem;
    margin-right: auto;
    background: var(--glass-primary);
    color: var(--text-primary);
    border-bottom-left-radius: 0.25rem;
}

.chat-message.error {
    margin-left: 1rem;
    margin-right: auto;
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #f44336;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.8;
}

.message-sender {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message-sender::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
}

.message-content {
    line-height: 1.6;
    font-size: 0.95rem;
}

.message-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
    font-size: 0.875rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-content pre {
    background: rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.message-content pre code {
    background: none;
    padding: 0;
    border: none;
}

.message-content strong {
    font-weight: 600;
    color: var(--text-accent);
}

.message-content em {
    font-style: italic;
    opacity: 0.9;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 0.75rem;
    background: var(--glass-primary);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease-out;
    max-width: 300px;
    box-shadow: var(--shadow-elevated);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-color: rgba(76, 175, 80, 0.3);
    background: rgba(76, 175, 80, 0.1);
}

.notification.success i {
    color: #4caf50;
}

.notification.error {
    border-color: rgba(244, 67, 54, 0.3);
    background: rgba(244, 67, 54, 0.1);
}

.notification.error i {
    color: #f44336;
}

.notification.info {
    border-color: rgba(33, 150, 243, 0.3);
    background: rgba(33, 150, 243, 0.1);
}

.notification.info i {
    color: #2196f3;
}

.notification i {
    font-size: 1.2em;
    flex-shrink: 0;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    margin-left: 1rem;
    margin-right: auto;
    background: var(--glass-primary);
    border-radius: 0.75rem;
    border-bottom-left-radius: 0.25rem;
    max-width: 80px;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--text-secondary);
    animation: typingDot 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDot {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Message Actions */
.message-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    opacity: 0;
    transition: opacity 0.2s ease-out;
}

.chat-message:hover .message-actions {
    opacity: 1;
}

.message-action {
    padding: 0.25rem 0.5rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease-out;
}

.message-action:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

/* Scroll to bottom button */
.scroll-to-bottom {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: var(--text-accent);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(20px);
    z-index: 10;
}

.scroll-to-bottom.show {
    opacity: 1;
    transform: translateY(0);
}

.scroll-to-bottom:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-elevated);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 0.25rem;
    height: 1rem;
    margin: 0.25rem 0;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.loading-skeleton.line-1 { width: 100%; }
.loading-skeleton.line-2 { width: 80%; }
.loading-skeleton.line-3 { width: 60%; }

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    opacity: 0.6;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.empty-state h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}
