#!/usr/bin/env node

/**
 * Final test to verify the aizen.showSettings command is working
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Final Settings Command Test');
console.log('==============================\n');

// Test 1: Check package.json has the command declared
console.log('1. 📦 Checking package.json command declaration...');
try {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const commands = pkg.contributes?.commands || [];
    const settingsCommand = commands.find(cmd => cmd.command === 'aizen.showSettings');
    
    if (settingsCommand) {
        console.log('   ✅ aizen.showSettings command declared in package.json');
        console.log(`   📝 Title: "${settingsCommand.title}"`);
        console.log(`   📂 Category: "${settingsCommand.category}"`);
    } else {
        console.log('   ❌ aizen.showSettings command NOT declared in package.json');
    }
} catch (error) {
    console.log(`   ❌ Error reading package.json: ${error.message}`);
}

// Test 2: Check compiled extension.js has the command registered
console.log('\n2. 🔧 Checking compiled extension.js...');
try {
    const extensionJs = fs.readFileSync('out/extension.js', 'utf8');
    
    if (extensionJs.includes('aizen.showSettings')) {
        console.log('   ✅ aizen.showSettings command found in compiled extension.js');
        
        // Check if it's properly registered
        if (extensionJs.includes('registerCommand') && extensionJs.includes('aizen.showSettings')) {
            console.log('   ✅ Command is properly registered with vscode.commands.registerCommand');
        } else {
            console.log('   ⚠️  Command found but registration might be incomplete');
        }
    } else {
        console.log('   ❌ aizen.showSettings command NOT found in compiled extension.js');
    }
} catch (error) {
    console.log(`   ❌ Error reading extension.js: ${error.message}`);
}

// Test 3: Check AizenSettingsProvider exists
console.log('\n3. 🎛️  Checking AizenSettingsProvider...');
const providerPath = 'out/providers/AizenSettingsProvider.js';
if (fs.existsSync(providerPath)) {
    console.log('   ✅ AizenSettingsProvider.js exists');
    
    try {
        const providerContent = fs.readFileSync(providerPath, 'utf8');
        if (providerContent.includes('createOrShow')) {
            console.log('   ✅ createOrShow method found in AizenSettingsProvider');
        } else {
            console.log('   ⚠️  createOrShow method not found in AizenSettingsProvider');
        }
    } catch (error) {
        console.log(`   ❌ Error reading AizenSettingsProvider: ${error.message}`);
    }
} else {
    console.log('   ❌ AizenSettingsProvider.js does not exist');
}

// Test 4: Check UI files exist
console.log('\n4. 🎨 Checking UI files...');
const uiFiles = [
    'out/ui/settings.html',
    'out/ui/settings.css',
    'out/ui/settings.js'
];

for (const file of uiFiles) {
    if (fs.existsSync(file)) {
        console.log(`   ✅ ${file} exists`);
    } else {
        console.log(`   ❌ ${file} missing`);
    }
}

console.log('\n📋 Next Steps:');
console.log('==============');
console.log('1. Reload VS Code window (Ctrl+Shift+P → "Developer: Reload Window")');
console.log('2. Open Command Palette (Ctrl+Shift+P)');
console.log('3. Search for "Aizen AI: Open Settings" or "aizen.showSettings"');
console.log('4. The command should now be available and working!');
console.log('\n🎯 If the command still doesn\'t work after reload, check the VS Code console for errors.');
